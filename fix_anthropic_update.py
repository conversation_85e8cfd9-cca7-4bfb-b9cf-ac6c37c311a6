#!/usr/bin/env python3
"""
Fix script voor Anthropic TypeError - Update naar nieuwste versie
Door Leon
"""

import subprocess
import sys
import os

def main():
    print("🔧 Anthropic TypeError Fix Script voor Leon")
    print("=" * 50)
    
    # Check of we in de juiste directory zijn
    if not os.path.exists("agent-zero-env"):
        print("❌ Error: agent-zero-env niet gevonden!")
        print("   Run dit script vanuit de agent-zero directory")
        sys.exit(1)
    
    print("\n📦 Huidige situatie checken...")
    
    # Activeer virtual environment en check versie
    activate_cmd = "source agent-zero-env/bin/activate && "
    
    # Check huidige versie
    result = subprocess.run(
        activate_cmd + "pip show langchain-anthropic",
        shell=True,
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        for line in result.stdout.split('\n'):
            if line.startswith('Version:'):
                current_version = line.split(':')[1].strip()
                print(f"   Huidige versie: {current_version}")
                break
    
    print("\n🎯 Drie opties beschikbaar:")
    print("1. Update naar nieuwste versie (aanbevolen)")
    print("2. Installeer specifieke werkende versie (0.3.0)")  
    print("3. Gebruik de automatische patch (verrassend!)")
    
    choice = input("\nKies optie (1/2/3): ").strip()
    
    if choice == "1":
        print("\n🚀 Updaten naar nieuwste versie...")
        cmd = activate_cmd + "pip install --upgrade langchain-anthropic"
        
    elif choice == "2":
        print("\n📌 Installeren van versie 0.3.0...")
        cmd = activate_cmd + "pip install langchain-anthropic==0.3.0"
        
    elif choice == "3":
        print("\n✨ De patch is al automatisch toegepast!")
        print("   Agent Zero zou nu zonder errors moeten draaien")
        print("\n💡 Tip: De patch wordt automatisch geladen bij het starten")
        print("   Locatie: python/helpers/anthropic_patch.py")
        return
        
    else:
        print("❌ Ongeldige keuze")
        sys.exit(1)
    
    # Voer commando uit
    print(f"\n🔄 Uitvoeren: {cmd}")
    result = subprocess.run(cmd, shell=True)
    
    if result.returncode == 0:
        print("\n✅ Succesvol uitgevoerd!")
        print("🎉 Start Agent Zero opnieuw om de wijzigingen te activeren")
        print("\n💡 Start commando:")
        print("   source agent-zero-env/bin/activate && python run_ui.py --host 0.0.0.0 --port 50001")
    else:
        print("\n❌ Er ging iets mis bij het uitvoeren")
        print("   Probeer het handmatig uit te voeren")

if __name__ == "__main__":
    main() 