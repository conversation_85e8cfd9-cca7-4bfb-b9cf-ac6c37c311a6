#!/bin/bash
# <PERSON>'s 24/7 Docker Management Script
# Lokale Agent Zero container die altijd draait

set -e

# Colors voor Nederlandse interface
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${PURPLE}"
echo "🐳 Leon's Agent Zero - 24/7 Docker Manager"
echo "=========================================="
echo "🇳🇱 Lokale container die altijd draait"
echo "=========================================="
echo -e "${NC}"

# Functie om container status te checken
check_status() {
    echo -e "${BLUE}📊 Container Status:${NC}"
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(leon-agent-zero|leon-thomas-tts)"; then
        echo -e "${GREEN}✅ Agent Zero containers draaien${NC}"
    else
        echo -e "${YELLOW}⚠️ Geen Agent Zero containers actief${NC}"
    fi
    echo ""
}

# Functie om logs te tonen
show_logs() {
    echo -e "${BLUE}📋 Laatste logs (druk Ctrl+C om te stoppen):${NC}"
    docker-compose logs -f --tail=50 agent-zero
}

# Functie om alles te stoppen
stop_all() {
    echo -e "${YELLOW}🛑 Stoppen van alle Agent Zero containers...${NC}"
    docker-compose down
    echo -e "${GREEN}✅ Containers gestopt${NC}"
}

# Functie om alles te starten
start_all() {
    echo -e "${BLUE}🚀 Starten van Agent Zero 24/7 containers...${NC}"
    
    # Check .env file
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ .env file niet gevonden!${NC}"
        echo "Maak eerst .env aan met je API keys"
        exit 1
    fi
    
    # Build en start containers
    docker-compose up -d --build
    
    echo -e "${GREEN}✅ Containers gestart!${NC}"
    echo -e "${BLUE}🌐 Agent Zero draait op: http://localhost:50001${NC}"
    echo -e "${BLUE}🎤 Thomas TTS draait op: http://localhost:8080${NC}"
    
    # Wacht even en check status
    sleep 5
    check_status
}

# Functie om containers te restarten
restart_all() {
    echo -e "${YELLOW}🔄 Herstarten van containers...${NC}"
    docker-compose restart
    echo -e "${GREEN}✅ Containers herstart${NC}"
}

# Functie om updates te doen
update_all() {
    echo -e "${BLUE}📦 Updaten van containers...${NC}"
    git pull
    docker-compose down
    docker-compose build --no-cache
    docker-compose up -d
    echo -e "${GREEN}✅ Update voltooid${NC}"
}

# Functie om containers te monitoren
monitor() {
    echo -e "${BLUE}📊 Real-time monitoring (druk Ctrl+C om te stoppen):${NC}"
    watch -n 2 'docker stats leon-agent-zero leon-thomas-tts --no-stream'
}

# Functie om in container te gaan
shell_access() {
    echo -e "${BLUE}🐚 Shell toegang tot Agent Zero container:${NC}"
    docker exec -it leon-agent-zero /bin/bash
}

# Functie om backup te maken
backup() {
    BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    echo -e "${BLUE}💾 Backup maken naar $BACKUP_DIR...${NC}"
    
    # Backup volumes
    docker run --rm -v agent-zero_agent-zero-data:/data -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/agent-zero-data.tar.gz -C /data .
    
    # Backup configs
    cp -r ./memory ./knowledge ./logs "$BACKUP_DIR/"
    
    echo -e "${GREEN}✅ Backup voltooid: $BACKUP_DIR${NC}"
}

# Main menu
show_menu() {
    echo -e "${PURPLE}Kies een optie:${NC}"
    echo "1. 🚀 Start Agent Zero 24/7"
    echo "2. 🛑 Stop alle containers"
    echo "3. 🔄 Herstart containers"
    echo "4. 📊 Toon status"
    echo "5. 📋 Toon logs"
    echo "6. 📊 Monitor resources"
    echo "7. 🐚 Shell toegang"
    echo "8. 📦 Update containers"
    echo "9. 💾 Backup maken"
    echo "0. ❌ Afsluiten"
    echo ""
}

# Main loop
while true; do
    show_menu
    read -p "Voer je keuze in (0-9): " choice
    
    case $choice in
        1)
            start_all
            ;;
        2)
            stop_all
            ;;
        3)
            restart_all
            ;;
        4)
            check_status
            ;;
        5)
            show_logs
            ;;
        6)
            monitor
            ;;
        7)
            shell_access
            ;;
        8)
            update_all
            ;;
        9)
            backup
            ;;
        0)
            echo -e "${GREEN}👋 Tot ziens Leon!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Ongeldige keuze. Probeer opnieuw.${NC}"
            ;;
    esac
    
    echo ""
    read -p "Druk Enter om door te gaan..."
    clear
done 