# <PERSON>'s MCP Integration Guide

## 🚀 MCP Servers voor Agent Zero

Deze guide beschrijft de custom Model Context Protocol (MCP) servers die ik heb toegevoegd aan Agent Zero.

### Toegevoegde Servers:

1. **Leon NeMo TTS Server** (`leon-nemo-tts`)
   - <PERSON> spraaksynthese integratie
   - Context-aware Dutch TTS
   - Emotie en stijl aanpassingen

2. **Leon Trading Data Server** (`leon-trading-data`)
   - Real-time crypto prijzen
   - Portfolio management
   - Trading alerts en P&L berekeningen

## 📁 Bestanden Structuur

```
agent-zero/
├── python/mcp_servers/
│   ├── __init__.py
│   ├── leon_nemo_mcp.py      # NeMo TTS MCP server
│   ├── leon_trading_mcp.py   # Trading data MCP server
│   └── README.md              # MCP documentatie
├── leon_mcp_config.json       # MCP configuratie
└── test_leon_mcp.py          # Test script
```

## 🔧 Configuratie

Om de MCP servers te activeren in Agent Zero:

1. **Update je Agent Zero configuratie** om MCP servers te laden:

```python
# In je Agent Zero settings, voeg toe:
"mcp_config_path": "leon_mcp_config.json"
```

2. **Of integreer direct** in de Agent Zero MCP middleware:

```python
# python/helpers/mcp_manager.py (als die bestaat)
# Voeg je custom servers toe aan de MCP registry
```

## 💡 Gebruik Voorbeelden

### Trading Commands:
```
"Wat is de Bitcoin prijs?"
"Toon mijn crypto portfolio"
"Bereken winst/verlies voor ETH"
"Laatste 5 trading alerts"
```

### TTS Commands:
```
"Laat Thomas zeggen: Hallo Leon, alles goed?"
"Thomas, vertel me enthousiast over de Bitcoin koers"
"Check of Thomas TTS service draait"
```

## 🔗 Integratie met Extensions

De MCP servers werken samen met:

1. **Leon Master AI Stack** (`python/extensions/leon_master_ai_stack.py`)
   - Voice-to-voice AI pipeline
   - Trading bot integratie
   - Multi-agent orchestration

2. **Leon TTS Extension** (`python/extensions/monologue_end/_99_leon_tts.py`)
   - Fallback voor directe TTS
   - Emotie detectie
   - Context awareness

## 🚀 Advanced Features

### Portfolio Tracking:
```python
# In leon_trading_mcp.py
PORTFOLIO = {
    "BTC": {"amount": 0.5, "avg_price": 45000},
    "ETH": {"amount": 2.5, "avg_price": 3200},
    "SOL": {"amount": 10, "avg_price": 120}
}
```

### Dynamic Voice Styles:
```python
# In leon_nemo_mcp.py
emotion_to_style = {
    "excited": "thomas_business",
    "serious": "thomas_urgent",
    "friendly": "thomas_casual",
    "casual": "thomas_casual",
    "neutral": "thomas_casual"
}
```

## 🧪 Testing

```bash
# Test individuele server
python python/mcp_servers/leon_trading_mcp.py

# Test alle servers
python test_leon_mcp.py

# Test API direct
curl -X POST http://localhost:8080/thomas/synthesize \
  -H "Content-Type: application/json" \
  -d '{"text": "Test Thomas TTS", "voice_style": "thomas_casual"}'
```

## 📝 Next Steps

1. **Database Integratie**: Vervang mock portfolio met echte database
2. **Meer Exchanges**: Binance, Kraken API's toevoegen
3. **Voice Trading**: "Thomas, koop 0.1 Bitcoin"
4. **Telegram Bot**: Trading alerts via Telegram

## 🎯 Benefits

- **Unified Interface**: Alle tools via Agent Zero chat
- **Context Aware**: MCP begrijpt conversatie context
- **Extensible**: Makkelijk nieuwe tools toevoegen
- **Real-time Data**: Live crypto prijzen en alerts

---

*Leon's Custom MCP Integration - Making Agent Zero Smarter* 🚀 