# 🤖 Telegram Bot Integration voor Agent Zero

Complete setup guide voor <PERSON>'s Telegram trading bot integratie.

## 📦 Wat is toegevoegd:

1. **Telegram Tool** (`python/tools/telegram_tool.py`)
   - Send messages, trading alerts, portfolio updates
   - Handle bot commands en webhooks
   - Interactive buttons support

2. **Webhook API** (`python/api/telegram_webhook.py`)
   - Receive Telegram updates
   - Process commands
   - Send messages via API

3. **Auto Alerts Extension** (`python/extensions/response_stream/telegram_alerts.py`)
   - Automatisch trading signals detecteren
   - Alerts versturen zonder manual trigger

4. **Configuration** (`telegram_config.json`)
   - Bot settings
   - Trading parameters
   - Alert thresholds

## 🚀 Setup Instructions:

### 1. Maak een Telegram Bot

1. Open Telegram en zoek naar `@BotFather`
2. Stuur `/newbot`
3. Kies een naam (bijv. "<PERSON>'s Trading Bot")
4. <PERSON><PERSON> een username (bijv. `@LeonTradingBot`)
5. <PERSON><PERSON><PERSON> de **Bot Token** die je krijgt

### 2. Configureer Agent Zero

```bash
# 1. Edit telegram_config.json
# Vervang YOUR_BOT_TOKEN_HERE met je echte token
{
  "bot_token": "7234567890:ABCdefGHIjklMNOpqrsTUVwxyz",
  "bot_username": "@LeonTradingBot"
}

# 2. Start Agent Zero
source agent-zero-env/bin/activate
python run_ui.py --host 0.0.0.0 --port 50001
```

### 3. Setup Bot Commands

In Agent Zero chat:
```
Setup mijn Telegram bot commands
```

Of via code:
```python
response = await agent.call_tool(
    "telegram_tool",
    action="set_commands"
)
```

### 4. Get Your Chat ID

1. Stuur een bericht naar je bot in Telegram
2. In Agent Zero, run:
```
Check Telegram updates
```
3. Zoek je chat ID in de response
4. Update `telegram_config.json`:
```json
{
  "default_chat_id": "123456789"
}
```

## 💬 Gebruik in Agent Zero:

### Basic Messages:
```
Stuur "Bitcoin is stijgend!" naar Telegram
```

### Trading Alerts:
```
Stuur een BTC buy alert naar Telegram met prijs €91,000
```

### Portfolio Updates:
```
Stuur mijn portfolio update naar Telegram
```

## 🔧 Advanced Features:

### 1. Webhook Setup (Production)

```bash
# 1. Gebruik ngrok voor local development
ngrok http 50001

# 2. Set webhook URL
curl -X POST https://api.telegram.org/bot{TOKEN}/setWebhook \
  -d "url=https://your-ngrok-url.ngrok.io/api/telegram/webhook"

# 3. Update config
{
  "webhook_url": "https://your-domain.com/api/telegram/webhook"
}
```

### 2. Auto Trading Alerts

De extension monitort automatisch Agent responses voor:
- Buy/Sell signals
- Price targets
- Trading opportunities

Patterns die getriggerd worden:
- "Buy signal for BTC"
- "ETH reached €3,000"
- "Bearish on SOL"

### 3. Interactive Buttons

```python
# In je tool/extension:
reply_markup = {
    "inline_keyboard": [[
        {"text": "📊 Chart", "callback_data": "chart_BTC"},
        {"text": "✅ Execute", "callback_data": "execute_buy_BTC"}
    ]]
}
```

## 📊 Trading Bot Commands:

| Command | Description | Example |
|---------|-------------|---------|
| `/start` | Start bot | - |
| `/portfolio` | Portfolio overzicht | - |
| `/price BTC` | Check prijs | `/price ETH` |
| `/buy BTC 0.01` | Koop order | `/buy SOL 10` |
| `/sell ETH 0.5` | Verkoop order | `/sell ADA 100` |
| `/alerts` | Beheer alerts | - |
| `/history` | Trade geschiedenis | - |

## 🔗 Integratie met MCP Servers:

De Telegram bot werkt samen met:
- `leon-trading-data` MCP server voor prijzen
- `leon-nemo-tts` voor voice alerts

```python
# Combineer tools:
price_data = await agent.call_tool("get_crypto_price", symbol="BTC")
await agent.call_tool("telegram_tool", 
    action="send_trading_alert",
    symbol="BTC",
    price=price_data["price"]
)
```

## ⚡ Quick Test:

```python
# Test script
import asyncio
from agent import Agent

async def test_telegram():
    agent = Agent()
    
    # Test message
    response = await agent.call_tool(
        "telegram_tool",
        action="send_message",
        text="🚀 Agent Zero Telegram Integration Active!"
    )
    print(response)
    
    # Test trading alert
    response = await agent.call_tool(
        "telegram_tool",
        action="send_trading_alert",
        symbol="BTC",
        alert_type="buy",
        message="Strong buy signal detected",
        price=91500
    )
    print(response)

asyncio.run(test_telegram())
```

## 🛡️ Security Tips:

1. **Never commit bot token** - Use .env file
2. **Whitelist chat IDs** - Only accept from known users
3. **Rate limiting** - Prevent spam
4. **Validate inputs** - Check command parameters

## 📈 Next Steps:

1. **Database Integration** - Store trade history
2. **Exchange APIs** - Real trading execution
3. **Advanced TA** - Technical indicators
4. **Multi-user Support** - Personal portfolios
5. **Voice Messages** - Thomas TTS integration

---

*Telegram Bot Integration by Leon - Making Agent Zero Social* 🚀 