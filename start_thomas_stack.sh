#!/bin/bash
# <PERSON>'s <PERSON> TTS Stack Startup Script
# Combines NeMo AI service + Agent Zero with hybrid TTS

echo "🚀 Starting <PERSON>'s Thomas TTS Stack..."

# Function to check if port is available
check_port() {
    if lsof -i :$1 > /dev/null 2>&1; then
        echo "❌ Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Function to start NeMo Thomas service
start_nemo_service() {
    echo "🤖 Starting NeMo Thomas TTS Service..."
    
    if check_port 8080; then
        cd agent_zero_service
        
        # Check if Docker is available
        if command -v docker &> /dev/null; then
            echo "🐳 Using Docker for NeMo service..."
            docker build -t leon-thomas-tts .
            docker run -d -p 8080:8080 --name thomas-tts leon-thomas-tts
        else
            echo "🐍 Using Python for NeMo service..."
            python thomas_tts_service.py &
        fi
        
        # Wait for service to start
        echo "⏳ Waiting for NeMo service to initialize..."
        sleep 10
        
        # Health check
        if curl -s http://localhost:8080/thomas/health > /dev/null; then
            echo "✅ NeMo Thomas service is healthy"
            return 0
        else
            echo "❌ NeMo Thomas service failed to start"
            return 1
        fi
    else
        return 1
    fi
}

# Function to start Agent Zero
start_agent_zero() {
    echo "🤖 Starting Agent Zero with Thomas integration..."
    
    if check_port 50001; then
        # Activate virtual environment
        source agent-zero-env/bin/activate
        
        # Start Agent Zero
        python run_ui.py --host 0.0.0.0 --port 50001 &
        AGENT_ZERO_PID=$!
        
        echo "⏳ Waiting for Agent Zero to initialize..."
        sleep 15
        
        # Health check
        if curl -s http://localhost:50001 > /dev/null; then
            echo "✅ Agent Zero is running on http://localhost:50001"
            echo "📝 Agent Zero PID: $AGENT_ZERO_PID"
            return 0
        else
            echo "❌ Agent Zero failed to start"
            return 1
        fi
    else
        return 1
    fi
}

# Function to test Thomas functionality
test_thomas() {
    echo "🗣️ Testing Thomas TTS functionality..."
    
    # Test hybrid Thomas implementation
    python -c "
import asyncio
import sys
sys.path.append('.')
from python.extensions.monologue_end._99_leon_tts import test_hybrid_thomas
asyncio.run(test_hybrid_thomas())
"
}

# Main execution
main() {
    echo "🎯 Leon's Thomas TTS Stack"
    echo "=========================="
    
    # Start NeMo service (optional - graceful fallback)
    if start_nemo_service; then
        echo "🤖 NeMo Thomas AI service running"
        NEMO_AVAILABLE=true
    else
        echo "⚠️  NeMo service not available - using macOS fallback"
        NEMO_AVAILABLE=false
    fi
    
    # Start Agent Zero
    if start_agent_zero; then
        echo "🚀 Agent Zero running with Thomas integration"
        
        # Show status
        echo ""
        echo "📊 Thomas TTS Stack Status:"
        echo "------------------------"
        echo "🤖 Agent Zero: http://localhost:50001"
        
        if [ "$NEMO_AVAILABLE" = true ]; then
            echo "🔊 NeMo Thomas AI: http://localhost:8080"
            echo "💡 Thomas will use NeMo AI for high-quality speech"
        else
            echo "🗣️  macOS Thomas: Built-in fallback"
            echo "💡 Thomas will use macOS 'say' command"
        fi
        
        echo ""
        echo "🎙️ Thomas Commands:"
        echo "- enable_nemo_thomas()    # Use AI speech"
        echo "- enable_macos_thomas()   # Use macOS speech"
        echo "- disable_thomas()        # Disable speech"
        echo "- check_nemo_service()    # Check AI service"
        
        # Optional: Run test
        read -p "🧪 Run Thomas TTS test? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            test_thomas
        fi
        
        echo "✅ Thomas TTS Stack fully operational!"
        echo "💬 Je kunt nu in Nederlands praten met Thomas in Agent Zero"
        
        # Keep script running
        echo "⏸️  Press Ctrl+C to stop all services..."
        wait
        
    else
        echo "❌ Failed to start Agent Zero"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    echo "🛑 Stopping Thomas TTS Stack..."
    
    # Stop Agent Zero
    if [ ! -z "$AGENT_ZERO_PID" ]; then
        kill $AGENT_ZERO_PID 2>/dev/null
    fi
    
    # Stop NeMo service
    docker stop thomas-tts 2>/dev/null
    docker rm thomas-tts 2>/dev/null
    
    # Kill any remaining processes
    pkill -f "thomas_tts_service.py" 2>/dev/null
    pkill -f "run_ui.py" 2>/dev/null
    
    echo "🏁 Thomas TTS Stack stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Run main function
main 