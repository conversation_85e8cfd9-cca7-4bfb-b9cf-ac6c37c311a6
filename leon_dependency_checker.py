#!/usr/bin/env python3
"""
<PERSON>'s Master AI Stack Dependency Checker
Comprehensive check voor alle services, dependencies en configuratie
"""

import asyncio
import json
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import aiohttp
import requests

from python.helpers.print_style import PrintStyle


class LeonDependencyChecker:
    """Comprehensive dependency checker voor Leon's AI Stack"""

    def __init__(self, config_path: str = "leon_master_ai_config.json"):
        self.style = PrintStyle()
        self.config_path = config_path
        self.config = self.load_config()
        self.results = {
            "services": {},
            "dependencies": {},
            "system": {},
            "security": {},
            "overall_status": "unknown",
        }

    def load_config(self) -> dict:
        """Load Leon's AI Stack configuration"""
        try:
            with open(self.config_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            self.style.error(f"❌ Config file niet gevonden: {self.config_path}")
            return {"leon_master_ai_stack": {}}
        except json.JSONDecodeError as e:
            self.style.error(f"❌ Invalid JSON in config: {e}")
            return {"leon_master_ai_stack": {}}

    async def check_all_dependencies(self) -> Dict:
        """Complete dependency check met parallel execution"""
        self.style.print("🎯 Leon's Master AI Stack Dependency Check")
        self.style.print("=" * 60)

        # Parallel checks using asyncio.gather voor performance
        tasks = [
            self.check_system_dependencies(),
            self.check_python_packages(),
            self.check_services_health(),
            self.check_ollama_models(),
            self.check_whisper_setup(),
            self.check_security_config(),
        ]

        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            self.style.error(f"❌ Dependency check failed: {e}")

        # Calculate overall status
        self.calculate_overall_status()

        # Report results
        self.report_results()

        return self.results

    async def check_system_dependencies(self):
        """Check system-level dependencies"""
        self.style.print("\n🔧 System Dependencies Check")

        system_deps = {
            "ffmpeg": {
                "command": ["ffmpeg", "-version"],
                "required": True,
                "description": "Audio processing voor Whisper",
            },
            "say": {
                "command": ["say", "-v", "?"],  # List voices instead of version
                "required": True,
                "description": "macOS TTS fallback",
            },
            "python": {
                "command": [sys.executable, "--version"],
                "required": True,
                "description": "Python runtime",
            },
        }

        for dep_name, dep_info in system_deps.items():
            try:
                result = subprocess.run(
                    dep_info["command"], capture_output=True, text=True, timeout=10
                )

                if result.returncode == 0:
                    self.style.print(f"✅ {dep_name}: Available")
                    self.results["system"][dep_name] = {
                        "status": "ok",
                        "version": result.stdout.split("\n")[0],
                    }
                else:
                    self.style.error(f"❌ {dep_name}: Failed")
                    self.results["system"][dep_name] = {
                        "status": "failed",
                        "error": result.stderr,
                    }

            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                self.style.error(
                    f"❌ {dep_name}: Not found - {dep_info['description']}"
                )
                self.results["system"][dep_name] = {
                    "status": "missing",
                    "error": str(e),
                }

    async def check_python_packages(self):
        """Check required Python packages"""
        self.style.print("\n📦 Python Packages Check")

        required_packages = ["aiohttp", "requests", "whisper", "gtts", "pygame"]

        for package in required_packages:
            try:
                __import__(package)
                self.style.print(f"✅ {package}: Installed")
                self.results["dependencies"][package] = {"status": "ok"}
            except ImportError:
                self.style.error(f"❌ {package}: Missing - run: pip install {package}")
                self.results["dependencies"][package] = {"status": "missing"}

        # Check optional packages
        optional_packages = {
            "nemo": "NeMo TTS (optional high-quality TTS)",
            "chromadb": "ChromaDB client (optional knowledge base)",
        }

        for package, description in optional_packages.items():
            try:
                __import__(package)
                self.style.print(f"🔵 {package}: Installed ({description})")
                self.results["dependencies"][package] = {
                    "status": "ok",
                    "optional": True,
                }
            except ImportError:
                self.style.warning(f"⚠️ {package}: Optional - {description}")
                self.results["dependencies"][package] = {
                    "status": "missing",
                    "optional": True,
                }

    async def check_services_health(self):
        """Check all AI services health in parallel"""
        self.style.print("\n🏥 Services Health Check")

        services = self.config.get("leon_master_ai_stack", {}).get("services", {})

        # Use asyncio.gather for parallel health checks
        health_tasks = []
        for service_name, service_config in services.items():
            if service_name != "whisper":  # Whisper is local, not a service
                health_tasks.append(
                    self.check_service_health(service_name, service_config)
                )

        try:
            await asyncio.gather(*health_tasks, return_exceptions=True)
        except Exception as e:
            self.style.error(f"❌ Service health check failed: {e}")

    async def check_service_health(self, service_name: str, service_config: dict):
        """Check individual service health"""
        url = service_config.get("url", "")
        timeout = service_config.get("timeout", 10)

        if not url:
            self.results["services"][service_name] = {"status": "no_url"}
            return

        try:
            async with aiohttp.ClientSession() as session:
                # Try health endpoint first, fallback to main URL
                health_endpoint = service_config.get("health_endpoint", "")
                check_url = f"{url}{health_endpoint}" if health_endpoint else url

                async with session.get(
                    check_url, timeout=aiohttp.ClientTimeout(total=timeout)
                ) as response:
                    if response.status == 200:
                        self.style.print(f"✅ {service_name}: Healthy ({check_url})")
                        self.results["services"][service_name] = {
                            "status": "healthy",
                            "url": check_url,
                            "response_code": response.status,
                        }
                    else:
                        self.style.warning(
                            f"⚠️ {service_name}: Unhealthy (HTTP {response.status})"
                        )
                        self.results["services"][service_name] = {
                            "status": "unhealthy",
                            "response_code": response.status,
                        }

        except asyncio.TimeoutError:
            self.style.error(f"❌ {service_name}: Timeout ({url})")
            self.results["services"][service_name] = {"status": "timeout"}
        except Exception as e:
            self.style.error(f"❌ {service_name}: Connection failed - {str(e)[:50]}")
            self.results["services"][service_name] = {
                "status": "failed",
                "error": str(e),
            }

    async def check_ollama_models(self):
        """Validate Ollama models volgens Leon's verbetering punt 2"""
        self.style.print("\n🦙 Ollama Models Validation")

        ollama_config = (
            self.config.get("leon_master_ai_stack", {})
            .get("services", {})
            .get("ollama", {})
        )
        models = ollama_config.get("models", {})

        if not models:
            self.style.warning("⚠️ Geen Ollama models geconfigureerd")
            return

        try:
            # Check available models via Ollama API
            ollama_url = ollama_config.get("url", "http://localhost:11434")

            async with aiohttp.ClientSession() as session:
                async with session.get(f"{ollama_url}/api/tags") as response:
                    if response.status == 200:
                        data = await response.json()
                        available_models = [
                            model["name"] for model in data.get("models", [])
                        ]

                        for model_type, model_name in models.items():
                            if model_name in available_models:
                                self.style.print(
                                    f"✅ Ollama {model_type}: {model_name}"
                                )
                                self.results["services"][f"ollama_{model_type}"] = {
                                    "status": "available",
                                    "model": model_name,
                                }
                            else:
                                self.style.error(
                                    f"❌ Ollama {model_type}: {model_name} niet gevonden"
                                )
                                self.style.print(
                                    f"   Beschikbare models: {', '.join(available_models[:3])}"
                                )
                                self.results["services"][f"ollama_{model_type}"] = {
                                    "status": "missing",
                                    "model": model_name,
                                }
                    else:
                        self.style.error(
                            "❌ Ollama API niet bereikbaar voor model check"
                        )

        except Exception as e:
            self.style.error(f"❌ Ollama model validation failed: {e}")

    async def check_whisper_setup(self):
        """Check Whisper setup volgens verbetering punt 3"""
        self.style.print("\n🎙️ Whisper Setup Check")

        whisper_config = (
            self.config.get("leon_master_ai_stack", {})
            .get("services", {})
            .get("whisper", {})
        )

        # Check ffmpeg specifically
        ffmpeg_path = whisper_config.get("ffmpeg_path", "ffmpeg")

        try:
            result = subprocess.run(
                [ffmpeg_path, "-version"], capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                self.style.print(f"✅ FFmpeg: Available at {ffmpeg_path}")
                self.results["system"]["ffmpeg_whisper"] = {
                    "status": "ok",
                    "path": ffmpeg_path,
                }
            else:
                self.style.error(f"❌ FFmpeg: Failed at {ffmpeg_path}")
                self.results["system"]["ffmpeg_whisper"] = {"status": "failed"}
        except FileNotFoundError:
            self.style.error(f"❌ FFmpeg: Not found at {ffmpeg_path}")
            self.style.print("   Install: brew install ffmpeg")
            self.results["system"]["ffmpeg_whisper"] = {"status": "missing"}

        # Check temp directory
        temp_dir = whisper_config.get("temp_dir", "/tmp/leon_audio")
        try:
            Path(temp_dir).mkdir(parents=True, exist_ok=True)
            self.style.print(f"✅ Audio temp dir: {temp_dir}")
            self.results["system"]["audio_temp_dir"] = {
                "status": "ok",
                "path": temp_dir,
            }
        except Exception as e:
            self.style.error(f"❌ Audio temp dir failed: {e}")
            self.results["system"]["audio_temp_dir"] = {"status": "failed"}

    async def check_security_config(self):
        """Check security configuration volgens punt 5"""
        self.style.print("\n🔒 Security Configuration Check")

        security_config = self.config.get("leon_master_ai_stack", {}).get(
            "security", {}
        )

        checks = {
            "sensitive_patterns": "Sensitive data pattern filtering",
            "max_prompt_log_length": "Prompt logging restriction",
            "enable_rate_limiting": "Rate limiting enabled",
        }

        for check_key, description in checks.items():
            if check_key in security_config:
                self.style.print(f"✅ {description}: Configured")
                self.results["security"][check_key] = {
                    "status": "configured",
                    "value": security_config[check_key],
                }
            else:
                self.style.warning(f"⚠️ {description}: Not configured")
                self.results["security"][check_key] = {"status": "missing"}

    def calculate_overall_status(self):
        """Calculate overall system status"""
        critical_failures = 0
        warnings = 0

        # Check critical systems
        for category in ["system", "dependencies", "services"]:
            for item, status in self.results.get(category, {}).items():
                if status.get("status") in [
                    "missing",
                    "failed",
                    "timeout",
                ] and not status.get("optional", False):
                    critical_failures += 1
                elif status.get("status") in ["unhealthy", "missing"] and status.get(
                    "optional", False
                ):
                    warnings += 1

        if critical_failures == 0:
            if warnings == 0:
                self.results["overall_status"] = "excellent"
            else:
                self.results["overall_status"] = "good"
        elif critical_failures <= 2:
            self.results["overall_status"] = "degraded"
        else:
            self.results["overall_status"] = "critical"

    def report_results(self):
        """Generate final status report"""
        self.style.print("\n" + "=" * 60)
        self.style.print("🎯 LEON'S MASTER AI STACK STATUS REPORT")
        self.style.print("=" * 60)

        status_emoji = {
            "excellent": "🟢",
            "good": "🟡",
            "degraded": "🟠",
            "critical": "🔴",
        }

        overall = self.results["overall_status"]
        self.style.print(f"\n{status_emoji[overall]} Overall Status: {overall.upper()}")

        # Service summary
        services = self.results.get("services", {})
        healthy_services = sum(
            1 for s in services.values() if s.get("status") == "healthy"
        )
        total_services = len(
            [s for s in services.keys() if not s.startswith("ollama_")]
        )

        self.style.print(f"🏥 Services: {healthy_services}/{total_services} healthy")

        # Dependencies summary
        deps = self.results.get("dependencies", {})
        installed_deps = sum(1 for d in deps.values() if d.get("status") == "ok")
        required_deps = sum(1 for d in deps.values() if not d.get("optional", False))

        self.style.print(
            f"📦 Dependencies: {installed_deps}/{len(deps)} installed ({required_deps} required)"
        )

        # Recommendations
        self.style.print(f"\n📋 Recommendations:")

        if overall == "critical":
            self.style.error("❌ Critical issues found - fix before using AI Stack")
        elif overall == "degraded":
            self.style.warning("⚠️ Some issues found - reduced functionality expected")
        elif overall == "good":
            self.style.print("✅ Minor issues - AI Stack should work well")
        else:
            self.style.print("🎉 All systems optimal - AI Stack ready!")

        # Save results for debugging
        with open("leon_dependency_results.json", "w") as f:
            json.dump(self.results, f, indent=2)

        self.style.print(f"\n💾 Full results saved to: leon_dependency_results.json")


async def main():
    """Main entry point"""
    checker = LeonDependencyChecker()
    await checker.check_all_dependencies()


if __name__ == "__main__":
    asyncio.run(main())
