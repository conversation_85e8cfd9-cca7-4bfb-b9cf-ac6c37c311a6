# Leon's Agent Zero - Complete Docker Setup
# Dockerfile voor Agent Zero met DeepSeek, Trading Bots & Telegram
FROM python:3.11-slim

WORKDIR /app
COPY . /app

# Install requirements
RUN pip install --upgrade pip && pip install -r requirements.txt

# Create necessary directories
RUN mkdir -p tmp logs memory knowledge

# Expose port (Railway will set PORT env var)
EXPOSE $PORT

# Create startup script voor Railway
RUN echo '#!/bin/bash\n\
  export PORT=${PORT:-50001}\n\
  echo "🚀 Starting Agent Zero on port $PORT"\n\
  python run_ui.py --host 0.0.0.0 --port $PORT\n\
  ' > /app/start.sh && chmod +x /app/start.sh

# Start Agent Zero
CMD ["/app/start.sh"] 