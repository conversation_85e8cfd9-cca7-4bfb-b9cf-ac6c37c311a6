#!/usr/bin/env python3
"""
Agent <PERSON> - <PERSON>'s Fixed Startup Script
Geen <PERSON> kosten, geen RFC errors
"""

import json
import os
import sys
from pathlib import Path

# Set environment variables to disable RFC
os.environ["RFC_AUTO_DOCKER"] = "false"
os.environ["RFC_PASSWORD"] = ""
os.environ["RFC_URL"] = ""

# Import original run_ui after setting env vars
sys.path.insert(0, ".")


# Create a minimal settings override
def create_local_settings():
    settings = {
        "chat_model_provider": "openai",
        "chat_model_name": "gpt-3.5-turbo",
        "util_model_provider": "openai",
        "util_model_name": "gpt-3.5-turbo",
        "embed_model_provider": "huggingface",
        "embed_model_name": "sentence-transformers/all-MiniLM-L6-v2",
        "rfc_auto_docker": False,
        "rfc_password": "",
        "rfc_url": "",
        "agent_prompts_subdir": "default",
        "agent_memory_subdir": "default",
        "agent_knowledge_subdir": "custom",
    }

    # Write to tmp/settings.json
    os.makedirs("tmp", exist_ok=True)
    with open("tmp/settings.json", "w") as f:
        json.dump(settings, f, indent=2)


def main():
    print("🤖 Agent Zero - Leon's Fixed Version")
    print("=====================================")
    print("✅ RFC uitgeschakeld")
    print("✅ Lokale modellen configuratie")
    print("✅ Geen API kosten")

    # Create local settings
    create_local_settings()

    # Import and run original
    try:
        import run_ui

        run_ui.run()
    except Exception as e:
        print(f"❌ Fout: {e}")
        print("💡 Probeer: python run_ui.py --host 0.0.0.0 --port 50002")


if __name__ == "__main__":
    main()
