#!/usr/bin/env python3
"""
Telegram Bot Runner voor Agent Zero
Start de bot in polling mode om commands te ontvangen
"""

import asyncio
import json
import time
from datetime import datetime

import requests


class TelegramBotRunner:
    def __init__(self):
        with open("telegram_config.json", "r") as f:
            self.config = json.load(f)
        self.bot_token = self.config["bot_token"]
        self.offset = 0

    def send_message(self, chat_id, text, reply_markup=None):
        """Send a message to Telegram"""
        url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
        data = {"chat_id": chat_id, "text": text, "parse_mode": "Markdown"}
        if reply_markup:
            data["reply_markup"] = reply_markup

        response = requests.post(url, json=data)
        return response.json()

    def get_crypto_price(self, symbol):
        """Get crypto price from CoinGecko"""
        try:
            symbol_map = {
                "BTC": "bitcoin",
                "ETH": "ethereum",
                "SOL": "solana",
                "ADA": "cardano",
            }

            coin_id = symbol_map.get(symbol.upper(), symbol.lower())
            response = requests.get(
                f"https://api.coingecko.com/api/v3/simple/price"
                f"?ids={coin_id}&vs_currencies=eur&include_24hr_change=true"
            )

            if response.status_code == 200:
                data = response.json()
                if coin_id in data:
                    price = data[coin_id]["eur"]
                    change = data[coin_id].get("eur_24h_change", 0)
                    return price, change
        except Exception as e:
            print(f"Price error: {e}")
        return None, None

    def handle_command(self, chat_id, command, args):
        """Handle bot commands"""

        if command == "/start":
            welcome = (
                "🤖 *Welcome to Leon's Trading Bot!*\n\n"
                "Ik ben gekoppeld aan Agent Zero en help je met:\n"
                "• Trading alerts en signalen\n"
                "• Portfolio tracking\n"
                "• Crypto prijzen\n\n"
                "*Beschikbare commando's:*\n"
                "/portfolio - Bekijk portfolio\n"
                "/price [symbol] - Check prijs\n"
                "/alerts - Beheer alerts\n"
                "/help - Alle commando's\n\n"
                "_Powered by Agent Zero_ 🚀"
            )

            buttons = {
                "inline_keyboard": [
                    [
                        {"text": "📊 Portfolio", "callback_data": "portfolio"},
                        {"text": "💰 Prijzen", "callback_data": "prices"},
                    ]
                ]
            }

            self.send_message(chat_id, welcome, buttons)

        elif command == "/help":
            help_text = (
                "📚 *Alle Commando's:*\n\n"
                "/start - Start de bot\n"
                "/portfolio - Portfolio overzicht\n"
                "/price [SYMBOL] - Check crypto prijs\n"
                "/alerts - Actieve price alerts\n"
                "/buy [SYMBOL] [AMOUNT] - Simuleer koop\n"
                "/sell [SYMBOL] [AMOUNT] - Simuleer verkoop\n"
                "/history - Trade geschiedenis\n\n"
                "💡 *Tip:* Je kunt ook gewoon chatten!\n"
                "Vraag bijvoorbeeld: _'Wat is de Bitcoin prijs?'_"
            )
            self.send_message(chat_id, help_text)

        elif command == "/portfolio":
            # Mock portfolio data
            portfolio = (
                "💼 *Portfolio Overview*\n\n"
                "🟢 *BTC*: 0.5 @ €45,000\n"
                "   Waarde: €45,858 (+1.9%)\n\n"
                "🟢 *ETH*: 2.5 @ €3,200\n"
                "   Waarde: €8,250 (+3.1%)\n\n"
                "🔴 *SOL*: 10 @ €120\n"
                "   Waarde: €1,150 (-4.2%)\n\n"
                "💎 *Totaal*: €55,258 (+1.8%)"
            )

            buttons = {
                "inline_keyboard": [
                    [
                        {"text": "🔄 Refresh", "callback_data": "refresh_portfolio"},
                        {"text": "📈 Charts", "callback_data": "charts"},
                    ]
                ]
            }

            self.send_message(chat_id, portfolio, buttons)

        elif command == "/price":
            if args:
                symbol = args[0].upper()
                price, change = self.get_crypto_price(symbol)

                if price:
                    emoji = "📈" if change > 0 else "📉"
                    sign = "+" if change > 0 else ""

                    message = (
                        f"💰 *{symbol} Prijs*\n\n"
                        f"Prijs: €{price:,.2f}\n"
                        f"24h: {sign}{change:.2f}% {emoji}\n\n"
                        f"_Laatste update: {datetime.now().strftime('%H:%M:%S')}_"
                    )
                else:
                    message = f"❌ Kan prijs voor {symbol} niet vinden"
            else:
                message = "ℹ️ Gebruik: /price [SYMBOL]\nVoorbeeld: /price BTC"

            self.send_message(chat_id, message)

        elif command == "/alerts":
            alerts = (
                "🔔 *Actieve Alerts*\n\n"
                "1️⃣ BTC > €95,000\n"
                "2️⃣ ETH < €3,000\n"
                "3️⃣ SOL > €125\n\n"
                "_Gebruik Agent Zero om alerts te beheren_"
            )
            self.send_message(chat_id, alerts)

        else:
            self.send_message(
                chat_id,
                f"❌ Onbekend commando: {command}\nType /help voor alle commando's",
            )

    def handle_callback(self, callback_query):
        """Handle button callbacks"""
        chat_id = callback_query["message"]["chat"]["id"]
        data = callback_query["data"]

        # Answer callback to remove loading
        answer_url = f"https://api.telegram.org/bot{self.bot_token}/answerCallbackQuery"
        requests.post(answer_url, json={"callback_query_id": callback_query["id"]})

        if data == "portfolio":
            self.handle_command(chat_id, "/portfolio", [])
        elif data == "prices":
            message = "💰 *Top Crypto Prijzen*\n\n" "Loading live prices..."
            self.send_message(chat_id, message)

            # Get prices for top coins
            for symbol in ["BTC", "ETH", "SOL"]:
                price, change = self.get_crypto_price(symbol)
                if price:
                    emoji = "🟢" if change > 0 else "🔴"
                    self.send_message(
                        chat_id, f"{emoji} *{symbol}*: €{price:,.2f} ({change:+.2f}%)"
                    )
                time.sleep(0.5)  # Small delay

    def run(self):
        """Run the bot in polling mode"""
        print(f"🤖 Telegram Bot gestart!")
        print(f"   Username: {self.config.get('bot_username', 'Unknown')}")
        print(f"   Chat ID: {self.config.get('default_chat_id', 'Not set')}")
        print(f"\n👉 Commands werken nu in Telegram!")
        print("   Druk Ctrl+C om te stoppen\n")

        while True:
            try:
                # Get updates
                url = f"https://api.telegram.org/bot{self.bot_token}/getUpdates"
                params = {"offset": self.offset, "timeout": 30}

                response = requests.get(url, params=params, timeout=35)

                if response.status_code == 200:
                    data = response.json()

                    if data["ok"] and data["result"]:
                        for update in data["result"]:
                            self.offset = update["update_id"] + 1

                            # Handle messages
                            if "message" in update:
                                message = update["message"]
                                chat_id = message["chat"]["id"]

                                if "text" in message:
                                    text = message["text"]
                                    username = message["from"].get(
                                        "username", "Unknown"
                                    )
                                    print(f"💬 @{username}: {text}")

                                    if text.startswith("/"):
                                        parts = text.split()
                                        command = parts[0]
                                        args = parts[1:] if len(parts) > 1 else []
                                        self.handle_command(chat_id, command, args)
                                    else:
                                        # Regular chat - echo for now
                                        reply = (
                                            f"🤖 Ik hoorde: _{text}_\n\n"
                                            "Voor AI responses, gebruik Agent Zero UI!\n"
                                            "Of type /help voor commando's."
                                        )
                                        self.send_message(chat_id, reply)

                            # Handle callbacks
                            elif "callback_query" in update:
                                self.handle_callback(update["callback_query"])

            except KeyboardInterrupt:
                print("\n\n👋 Bot gestopt!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                time.sleep(5)  # Wait before retry


if __name__ == "__main__":
    bot = TelegramBotRunner()
    bot.run()
