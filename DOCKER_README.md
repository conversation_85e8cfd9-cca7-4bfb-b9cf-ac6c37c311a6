# 🚀 <PERSON>'s Agent Zero - Docker Deployment

**Complete AI Development Hub met DeepSeek, Trading Bots & Telegram**

## 🎯 Quick Start

### 1. <PERSON><PERSON><PERSON> keer opzetten
```bash
# Kopieer environment template
cp .env.template .env

# Bewerk .env en voeg je API keys toe
nano .env  # of je favoriete editor

# Deploy alles in één commando
./docker-build.sh deploy
```

### 2. Toegang tot je Agent Zero
- **Web UI**: http://localhost:50001
- **Direct Port**: http://localhost:50002  
- **SSH Access**: `ssh leon@localhost -p 50022` (wachtwoord: `leon2024`)

### 3. Beheer commando's
```bash
./docker-build.sh status    # Toon status
./docker-build.sh logs      # Toon live logs
./docker-build.sh restart   # Herstart services
./docker-build.sh stop      # Stop alles
./docker-build.sh clean     # Volledige cleanup
```

## 🔧 Configuratie

### Minimale .env setup:
```bash
# DeepSeek API (VERPLICHT)
API_KEY_DEEPSEEK=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

# Trading (OPTIONEEL)
MEXC_API_KEY=mx0vglmGyrf9LW7tY9
MEXC_API_SECRET=b5bb5493128b483c9ebe199d013042c1
TELEGRAM_TRADING_PASSWORD=Fatima23

# Telegram Bot (OPTIONEEL)
TELEGRAM_BOT_TOKEN=**********:AAH_4ILh_DFX-jmMZQjK0Z3b6X_BZf0Ddts
```

## 📊 Services

De Docker setup draait:
- **Agent Zero** (hoofdapplicatie)
- **Nginx** (reverse proxy)
- **SSH Server** (remote access)
- **Health Monitoring**

## 🎛️ Geavanceerd

### Logs bekijken:
```bash
docker-compose logs -f agent-zero    # Alleen Agent Zero
docker-compose logs -f               # Alle services
```

### In container gaan:
```bash
docker exec -it leon-agent-zero bash
```

### Data backup:
```bash
docker cp leon-agent-zero:/app/memory ./backup/memory
docker cp leon-agent-zero:/app/knowledge ./backup/knowledge
```

## 🚨 Troubleshooting

### Container start niet:
```bash
docker-compose down
./docker-build.sh clean
./docker-build.sh deploy
```

### Port conflicts:
Pas poorten aan in `docker-compose.yml`:
```yaml
ports:
  - "50001:80"      # Verander 50001 naar andere poort
  - "50002:50001"   # Verander 50002 naar andere poort
```

## 🎯 Leon's Features Enabled

✅ **DeepSeek AI Models** - Chat, Code, Vision
✅ **MEXC Trading Bot** - Automated trading
✅ **Telegram Integration** - Bot commands
✅ **Thomas TTS** - Nederlandse spraaksynthese
✅ **SSH Access** - Remote development
✅ **Health Monitoring** - Automatic restarts
✅ **Persistent Data** - Memory & knowledge opslag

---
🇳🇱 **Made for Leon - Dutch AI Developer** 