#!/usr/bin/env python3
"""
<PERSON>'s Agent Zero Environment Setup
Configureert de .env file met Claude API en bot instellingen
"""

import os
import shutil
from pathlib import Path


def setup_leon_environment():
    """
    Setup Leon's Agent Zero environment met Claude API configuratie
    """
    print("🇳🇱 <PERSON>'s Agent Zero Environment Setup")
    print("=" * 50)

    # Pad naar bestanden
    env_template = Path("env_claude.txt")
    env_file = Path(".env")

    try:
        # Kopieer env_claude.txt naar .env
        if env_template.exists():
            shutil.copy(env_template, env_file)
            print(f"✅ Environment bestand aangemaakt: {env_file}")
        else:
            print(f"❌ Template bestand niet gevonden: {env_template}")
            return False

        # Controleer of .env correct is aangemaakt
        if env_file.exists():
            with open(env_file, "r") as f:
                content = f.read()

            # Verificatie van belangrijke instellingen
            checks = [
                ("Claude API Key", "sk-ant-api03" in content),
                ("Telegram Token", "8071357175" in content),
                ("RFC Disabled", "RFC_ENABLED=false" in content),
                ("<PERSON>'s Settings", "DEVELOPER_NAME=Leon" in content),
                ("Dutch Language", "DEVELOPER_LANG=nl" in content),
            ]

            print("\n🔍 Configuratie verificatie:")
            all_good = True
            for check_name, is_ok in checks:
                status = "✅" if is_ok else "❌"
                print(f"{status} {check_name}")
                if not is_ok:
                    all_good = False

            if all_good:
                print("\n🎉 Alle configuraties zijn correct ingesteld!")
                print("\n🚀 Start Agent Zero met:")
                print(
                    "source agent-zero-env/bin/activate && python run_ui.py --host 0.0.0.0 --port 50001"
                )
                return True
            else:
                print("\n⚠️  Sommige configuraties zijn mogelijk niet correct.")
                return False

    except Exception as e:
        print(f"❌ Fout tijdens setup: {e}")
        return False


def verify_claude_connection():
    """
    Test de Claude API verbinding
    """
    try:
        import anthropic

        # Laad environment variabelen
        from dotenv import load_dotenv

        load_dotenv()

        api_key = os.getenv("API_KEY_ANTHROPIC")
        if not api_key or api_key.startswith("sk-ant-..."):
            print("❌ Claude API key niet correct ingesteld")
            return False

        # Test verbinding
        client = anthropic.Anthropic(api_key=api_key)

        # Simpele test call
        response = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=10,
            messages=[{"role": "user", "content": "Zeg hallo in het Nederlands"}],
        )

        if response.content:
            print("✅ Claude API verbinding succesvol!")
            print("📝 Test response ontvangen")
            return True
        else:
            print("❌ Claude API response leeg")
            return False

    except ImportError:
        print("⚠️  Anthropic library niet geïnstalleerd. Run: pip install anthropic")
        return False
    except Exception as e:
        print(f"❌ Claude API test gefaald: {e}")
        return False


def main():
    """
    Hoofdfunctie voor Leon's environment setup
    """
    print("🤖 Leon's Agent Zero - Claude 3.5 Sonnet Setup")
    print("=" * 60)

    # Setup environment
    if setup_leon_environment():
        print("\n" + "=" * 60)

        # Test Claude verbinding
        print("\n🔗 Claude API verbinding testen...")
        if verify_claude_connection():
            print("\n🎯 Setup voltooid! Je kunt Agent Zero nu starten.")
        else:
            print("\n⚠️  Setup voltooid, maar Claude API test gefaald.")
            print("Controleer je API key en internetverbinding.")
    else:
        print("\n❌ Setup gefaald. Controleer de bestanden en probeer opnieuw.")


if __name__ == "__main__":
    main()
