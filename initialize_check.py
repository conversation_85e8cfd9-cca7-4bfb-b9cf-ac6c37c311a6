#!/usr/bin/env python3
"""
Agent Zero - Initialisatie Check Script
Leon's persoonlijke setup verificatie tool
"""

import json
import os
import subprocess
import sys
from pathlib import Path


def print_status(message, status="INFO"):
    """Print status met kleurcodering"""
    colors = {
        "OK": "\033[92m✓\033[0m",
        "ERROR": "\033[91m✗\033[0m",
        "WARNING": "\033[93m⚠\033[0m",
        "INFO": "\033[94mℹ\033[0m",
    }
    print(f"{colors.get(status, '•')} {message}")


def check_python_version():
    """Controleer Python versie"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print_status(f"Python {version.major}.{version.minor}.{version.micro}", "OK")
        return True
    else:
        print_status(
            f"Python {version.major}.{version.minor}.{version.micro} - Vereist: 3.8+",
            "ERROR",
        )
        return False


def check_requirements():
    """Controleer of requirements.txt bestaat"""
    if Path("requirements.txt").exists():
        print_status("requirements.txt gevonden", "OK")
        return True
    else:
        print_status("requirements.txt niet gevonden", "ERROR")
        return False


def check_docker():
    """Controleer Docker installatie"""
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print_status(f"Docker beschikbaar: {result.stdout.strip()}", "OK")
            return True
        else:
            print_status("Docker niet beschikbaar", "ERROR")
            return False
    except FileNotFoundError:
        print_status("Docker niet geïnstalleerd", "ERROR")
        return False


def check_key_files():
    """Controleer essentiële bestanden"""
    key_files = ["agent.py", "run_ui.py", "run_cli.py", "models.py", "initialize.py"]

    missing_files = []
    for file in key_files:
        if Path(file).exists():
            print_status(f"{file} gevonden", "OK")
        else:
            print_status(f"{file} ontbreekt", "ERROR")
            missing_files.append(file)

    return len(missing_files) == 0


def check_directories():
    """Controleer essentiële directories"""
    dirs = [
        "python/api",
        "python/tools",
        "python/helpers",
        "webui",
        "prompts/default",
        "docs",
    ]

    missing_dirs = []
    for dir_path in dirs:
        if Path(dir_path).exists():
            print_status(f"Directory {dir_path} gevonden", "OK")
        else:
            print_status(f"Directory {dir_path} ontbreekt", "WARNING")
            missing_dirs.append(dir_path)

    return len(missing_dirs) == 0


def create_env_template():
    """Maak een .env template aan als deze niet bestaat"""
    env_path = Path(".env")
    example_env_path = Path("example.env")

    if not env_path.exists():
        if example_env_path.exists():
            # Kopieer example.env naar .env
            with open(example_env_path, "r") as src, open(env_path, "w") as dst:
                dst.write(src.read())
            print_status(".env aangemaakt van example.env", "OK")
        else:
            # Maak basis .env aan
            with open(env_path, "w") as f:
                f.write("# Agent Zero Environment Variables\n")
                f.write("# Voeg hier je API keys toe\n")
                f.write("OPENAI_API_KEY=your_openai_key_here\n")
                f.write("ANTHROPIC_API_KEY=your_anthropic_key_here\n")
            print_status("Basis .env bestand aangemaakt", "OK")
    else:
        print_status(".env bestand bestaat al", "OK")


def send_test_message():
    """Verzend test bericht"""
    print_status("Agent Zero initialisatie succesvol! 🚀", "OK")
    print_status("Bot configuratie geladen van bot_config.json", "INFO")
    print_status(
        "Architectuur overzicht beschikbaar in docs/architecture_overview.json", "INFO"
    )


def main():
    """Hoofdfunctie voor alle checks"""
    print("=" * 50)
    print("🤖 Agent Zero - Leon's Setup Verificatie")
    print("=" * 50)

    checks = [
        ("Python versie", check_python_version),
        ("Requirements bestand", check_requirements),
        ("Docker installatie", check_docker),
        ("Essentiële bestanden", check_key_files),
        ("Directory structuur", check_directories),
    ]

    results = []
    for name, check_func in checks:
        print(f"\n📋 {name}:")
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print_status(f"Fout tijdens {name}: {e}", "ERROR")
            results.append(False)

    print(f"\n🔧 Configuratie setup:")
    create_env_template()

    print(f"\n📊 Resultaten:")
    passed = sum(results)
    total = len(results)
    print_status(
        f"{passed}/{total} checks geslaagd", "OK" if passed == total else "WARNING"
    )

    if passed >= total - 1:  # Allow 1 failure
        send_test_message()

        # Toon volgende stappen
        print(f"\n🎯 Volgende stappen voor Leon:")
        print_status("1. Installeer Docker Desktop als dat nog niet gebeurd is", "INFO")
        print_status("2. Voeg API keys toe aan .env bestand", "INFO")
        print_status(
            "3. Draai: python run_ui.py om de web interface te starten", "INFO"
        )
        print_status(
            "4. Bekijk bot_config.json voor Telegram/WhatsApp integratie", "INFO"
        )
        print_status(
            "5. Check docs/architecture_overview.json voor systeem overzicht", "INFO"
        )
    else:
        print_status("Enkele kritieke issues gevonden - fix deze eerst", "WARNING")


if __name__ == "__main__":
    main()
