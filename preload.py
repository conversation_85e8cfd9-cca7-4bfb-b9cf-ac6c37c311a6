import asyncio
import traceback

import models
from python.helpers import runtime, settings, whisper
from python.helpers.print_style import PrintStyle

PrintStyle().print("Running preload...")
runtime.initialize()


async def preload():
    """
    Preload models voor Agent Zero
    Configureerbaar via settings voor optimale performance
    """
    # Configuratie opties voor preload gedrag
    preload_config = {
        "check_stt_model_value": True,
        "add_embedding_support_for_all_providers": True,
        "add_tracebacks_to_errors": True,
        "optionally_log_preload_returns": True,
    }

    try:
        set = settings.get_default_settings()

        # preload whisper model
        async def preload_whisper():
            try:
                if not preload_config.get("check_stt_model_value", True):
                    return None

                # Veiligheidscheck voor STT model size
                stt_model_size = set.get("stt_model_size", "base")
                if "stt_model_size" not in set:
                    PrintStyle().error("No STT model size set in settings")
                    return None

                result = await whisper.preload(stt_model_size)
                if preload_config.get("optionally_log_preload_returns", False):
                    PrintStyle().print(
                        f"Whisper preload result: {type(result).__name__}"
                    )
                return result
            except Exception as e:
                if preload_config.get("add_tracebacks_to_errors", True):
                    PrintStyle().error(
                        f"Error in preload_whisper: {traceback.format_exc()}"
                    )
                else:
                    PrintStyle().error(f"Error in preload_whisper: {e}")
                return None

        # preload embedding model - alle providers ondersteunen
        async def preload_embedding():
            try:
                provider_name = set.get("embed_model_provider", "HUGGINGFACE")
                model_name = set.get(
                    "embed_model_name", "sentence-transformers/all-MiniLM-L6-v2"
                )

                # Validatie van model instellingen
                if "embed_model_provider" not in set:
                    PrintStyle().warning(
                        "No embed_model_provider set in settings, using HuggingFace"
                    )
                if "embed_model_name" not in set:
                    PrintStyle().warning(
                        "No embed_model_name set in settings, using default"
                    )

                if not preload_config.get(
                    "add_embedding_support_for_all_providers", True
                ):
                    # Oude logica - alleen HuggingFace
                    if provider_name == models.ModelProvider.HUGGINGFACE.name:
                        emb_mod = models.get_huggingface_embedding(model_name)
                        emb_txt = await emb_mod.aembed_query("test")
                        return emb_txt
                    return None

                # Nieuwe logica - alle providers ondersteunen
                emb_mod = None

                if provider_name == models.ModelProvider.HUGGINGFACE.name:
                    emb_mod = models.get_huggingface_embedding(model_name)
                elif provider_name == models.ModelProvider.OPENAI.name:
                    emb_mod = models.get_openai_embedding(model_name)
                elif provider_name == models.ModelProvider.OPENAI_AZURE.name:
                    emb_mod = models.get_openai_azure_embedding(model_name)
                elif provider_name == models.ModelProvider.LMSTUDIO.name:
                    emb_mod = models.get_lmstudio_embedding(model_name)
                elif provider_name == models.ModelProvider.OLLAMA.name:
                    emb_mod = models.get_ollama_embedding(model_name)
                elif provider_name == models.ModelProvider.OPENROUTER.name:
                    emb_mod = models.get_openrouter_embedding(model_name)
                elif provider_name == models.ModelProvider.SAMBANOVA.name:
                    emb_mod = models.get_sambanova_embedding(model_name)
                elif provider_name == models.ModelProvider.OTHER.name:
                    emb_mod = models.get_other_embedding(model_name)
                else:
                    PrintStyle().warning(
                        f"No preload logic for provider: {provider_name}"
                    )
                    return None

                if emb_mod:
                    # Test embedding met kleine query
                    emb_txt = await emb_mod.aembed_query("test")

                    # Validatie van output
                    if emb_txt != "test" and len(emb_txt) > 0:
                        if preload_config.get("optionally_log_preload_returns", False):
                            PrintStyle().print(
                                f"Embedding preload result: {len(emb_txt)} dimensions"
                            )
                        return emb_txt
                    else:
                        PrintStyle().warning("Unexpected embedding output")
                        return None

                return None

            except Exception as e:
                if preload_config.get("add_tracebacks_to_errors", True):
                    PrintStyle().error(
                        f"Error in preload_embedding: {traceback.format_exc()}"
                    )
                else:
                    PrintStyle().error(f"Error in preload_embedding: {e}")
                return None

        # async tasks to preload
        tasks = [preload_whisper(), preload_embedding()]

        # Voer preload taken uit en controleer resultaten
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Controleer return values van preload functies
        whisper_result, embedding_result = results

        # Log resultaten als configured
        if preload_config.get("optionally_log_preload_returns", False):
            PrintStyle().print(
                f"Preload results - Whisper: {type(whisper_result).__name__}, Embedding: {type(embedding_result).__name__}"
            )

        # Check voor exceptions in results
        failed_tasks = []
        if isinstance(whisper_result, Exception):
            failed_tasks.append(f"Whisper: {whisper_result}")
        if isinstance(embedding_result, Exception):
            failed_tasks.append(f"Embedding: {embedding_result}")

        if failed_tasks:
            PrintStyle().warning(
                f"Some preload tasks failed: {', '.join(failed_tasks)}"
            )
        else:
            PrintStyle().print("✅ Preload completed successfully")

        return {
            "whisper": whisper_result,
            "embedding": embedding_result,
            "config": preload_config,
        }

    except Exception as e:
        if preload_config.get("add_tracebacks_to_errors", True):
            PrintStyle().error(f"Error in preload: {traceback.format_exc()}")
        else:
            PrintStyle().error(f"Error in preload: {e}")
        return None


# preload transcription model en embedding models
if __name__ == "__main__":
    result = asyncio.run(preload())
    if result:
        PrintStyle().print("🚀 Leon's Agent Zero preload systeem actief!")
