{"chat_model_provider": "openrouter", "chat_model_name": "deepseek/deepseek-r1", "chat_model_kwargs": {"temperature": "0.7", "base_url": "https://openrouter.ai/api/v1"}, "chat_model_ctx_length": 64000, "chat_model_ctx_history": 0.8, "chat_model_vision": true, "chat_model_rl_requests": 0, "chat_model_rl_input": 0, "chat_model_rl_output": 0, "util_model_provider": "openrouter", "util_model_name": "deepseek/deepseek-r1", "util_model_kwargs": {"temperature": "0.3", "base_url": "https://openrouter.ai/api/v1"}, "util_model_ctx_length": 64000, "util_model_ctx_input": 0.7, "util_model_rl_requests": 0, "util_model_rl_input": 0, "util_model_rl_output": 0, "embed_model_provider": "HUGGINGFACE", "embed_model_name": "sentence-transformers/all-MiniLM-L6-v2", "embed_model_kwargs": {}, "embed_model_rl_requests": 0, "embed_model_rl_input": 0, "browser_model_provider": "openrouter", "browser_model_name": "deepseek/deepseek-r1", "browser_model_vision": true, "browser_model_kwargs": {"temperature": "0.7", "base_url": "https://openrouter.ai/api/v1"}, "api_keys": {"API_KEY_OPENROUTER": "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", "API_KEY_ANTHROPIC": "************************************************************************************************************"}, "auth_login": "", "auth_password": "", "root_password": "", "agent_prompts_subdir": "default", "agent_memory_subdir": "leon_main", "agent_knowledge_subdir": "leon_dev", "rfc_auto_docker": true, "rfc_url": "localhost", "rfc_password": "", "rfc_port_http": 55080, "rfc_port_ssh": 55022, "stt_model_size": "turbo", "stt_language": "nl", "stt_silence_threshold": 0.3, "stt_silence_duration": 1000, "stt_waiting_timeout": 2000, "mcp_servers": "{\n    \"mcpServers\": {}\n}", "mcp_client_init_timeout": 5, "mcp_client_tool_timeout": 120, "mcp_server_enabled": false, "mcp_server_token": ""}