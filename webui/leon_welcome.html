<!DOCTYPE html>
<html lang="nl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> bij <PERSON>'s Agent Zero</title>
    <style>
      body {
        font-family: "Rubi<PERSON>", Arial, sans-serif;
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
        color: #e8e8e8;
        margin: 0;
        padding: 20px;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .welcome-container {
        max-width: 600px;
        background: rgba(17, 17, 17, 0.9);
        border-radius: 20px;
        padding: 40px;
        border: 1px solid #333;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .logo {
        background: linear-gradient(45deg, #ff6600, #0066cc);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
      }

      .subtitle {
        color: #cccccc;
        font-size: 1.2rem;
        margin-bottom: 20px;
      }

      .features {
        margin: 30px 0;
      }

      .feature {
        display: flex;
        align-items: center;
        margin: 15px 0;
        padding: 15px;
        background: rgba(255, 102, 0, 0.1);
        border-radius: 10px;
        border-left: 3px solid #ff6600;
      }

      .feature-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        width: 30px;
        text-align: center;
      }

      .feature-text {
        flex: 1;
      }

      .feature-title {
        font-weight: 600;
        color: #ff6600;
        margin-bottom: 5px;
      }

      .feature-desc {
        color: #cccccc;
        font-size: 0.9rem;
      }

      .start-button {
        display: block;
        width: 100%;
        background: linear-gradient(45deg, #ff6600, #0066cc);
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        margin-top: 30px;
      }

      .start-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(255, 102, 0, 0.3);
      }

      .footer {
        text-align: center;
        margin-top: 30px;
        color: #666;
        font-size: 0.9rem;
      }

      .badge {
        background: linear-gradient(45deg, #ff6600, #0066cc);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
        margin-top: 10px;
      }

      .claude-details {
        margin-top: 15px;
        padding: 15px;
        background: rgba(124, 58, 237, 0.1);
        border-radius: 10px;
        border: 1px solid rgba(124, 58, 237, 0.3);
        text-align: center;
      }

      .claude-model {
        background: linear-gradient(45deg, #7c3aed, #a855f7);
        color: white;
        padding: 4px 10px;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-right: 10px;
        display: inline-block;
      }

      .context-info {
        background: linear-gradient(45deg, #ff6600, #0066cc);
        color: white;
        padding: 4px 10px;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
      }
    </style>
  </head>
  <body>
    <div class="welcome-container">
      <div class="header">
        <h1 class="logo">🇳🇱 Agent Zero</h1>
        <p class="subtitle">Leon's AI Development Hub</p>
        <div class="badge">🤖 Nederlandse AI-ontwikkelaar</div>
      </div>

      <div class="features">
        <div class="feature">
          <div class="feature-icon">🤖</div>
          <div class="feature-text">
            <div class="feature-title">Telegram & WhatsApp Bots</div>
            <div class="feature-desc">
              Ontwikkel en beheer je chatbots direct vanuit Agent Zero
            </div>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">📈</div>
          <div class="feature-text">
            <div class="feature-title">Trading Bots</div>
            <div class="feature-desc">
              Bouw geavanceerde trading algoritmes met AI-ondersteuning
            </div>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">🎨</div>
          <div class="feature-text">
            <div class="feature-title">Figma naar Code</div>
            <div class="feature-desc">
              Automatische conversie van designs naar werkende applicaties
            </div>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">📱</div>
          <div class="feature-text">
            <div class="feature-title">PWA & Mobile Apps</div>
            <div class="feature-desc">
              Progressive Web Apps en mobiele applicatie ontwikkeling
            </div>
          </div>
        </div>

        <div class="feature">
          <div class="feature-icon">🔧</div>
          <div class="feature-text">
            <div class="feature-title">Workflow Automation</div>
            <div class="feature-desc">
              AI-agents voor geautomatiseerde workflows en CRM-integratie
            </div>
          </div>
        </div>
      </div>

      <a href="/" class="start-button"> 🚀 Start Agent Zero </a>

      <div class="footer">
        <p>
          Versie {{version_no}} •
          <strong>Claude 3.5 Sonnet</strong> geconfigureerd
        </p>
        <p>🚀 Geoptimaliseerd voor Nederlandse AI-ontwikkeling</p>
        <div class="claude-details">
          <span class="claude-model">🤖 Claude 3.5 Sonnet-20241022</span>
          <span class="context-info">📊 200K Context Window</span>
        </div>
      </div>
    </div>

    <script>
      // Auto-redirect naar hoofdinterface na 5 seconden
      setTimeout(() => {
        if (confirm("Agent Zero is klaar! Wil je naar de hoofdinterface?")) {
          window.location.href = "/";
        }
      }, 5000);
    </script>
  </body>
</html>
