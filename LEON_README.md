# 🤖 Agent Zero - <PERSON>'s Nederlandse AI Development Setup

## 📋 **Overzicht**
Complete installatiegids voor Agent Zero met Claude 3.5 Sonnet configuratie, speciaal voor Nederlandse AI-ontwikkeling met focus op Telegram/WhatsApp bots, trading bots en CRM-systemen.

---

## ⚡ **Snelle Start**

### **1. Environment Activeren**
```bash
cd /Users/<USER>/Downloads/Developer/dev/agent-zero
source agent-zero-env/bin/activate
```

### **2. Agent Zero Starten**
```bash
python run_ui.py --host 0.0.0.0 --port 50001
```

### **3. Web Interface**
Open: **http://localhost:50001**

---

## 🔧 **Volledige Configuratie**

### **Stap 1: Environment Variabelen (.env bestand)**

Maak `.env` bestand in de root directory:

```bash
# Agent Zero - Leon's Nederlandse AI Development Environment
# Claude 3.5 Sonnet Configuration

API_KEY_ANTHROPIC=************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************

# RFC disabled - geen Docker problemen
RFC_AUTO_DOCKER=false
RFC_ENABLED=false
DISABLE_RFC=true

# Leon's Web UI settings
WEB_UI_HOST=0.0.0.0
WEB_UI_PORT=50001

# Leon's Developer Settings
DEVELOPER_NAME=Leon
DEVELOPER_LANG=nl
BOT_DEVELOPMENT=true
TRADING_BOTS=true
FIGMA_TO_CODE=true

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_ENABLED=true

# WhatsApp Bot Configuration  
WHATSAPP_BUSINESS_ID=**********
WHATSAPP_PHONE_ID=**********
WHATSAPP_ENABLED=true

# Claude Model Configuration
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=8192
CLAUDE_TEMPERATURE=0.6
CLAUDE_CONTEXT_LENGTH=200000

# Development Environment
NODE_ENV=development
PYTHONPATH=.
```

### **Stap 2: Models.py Reparatie**

In `models.py` regel 44, zorg dat ModelProvider.ANTHROPIC correct is:

```python
class ModelProvider(Enum):
    ANTHROPIC = "ANTHROPIC"  # NIET een hardcoded API key!
    CHUTES = "Chutes"
    DEEPSEEK = "DeepSeek"
    # ... rest blijft hetzelfde
```

### **Stap 3: Claude Settings Configuratie**

In `tmp/settings.json` en `settings_claude.json`:

```json
{
  "chat_model": {
    "provider": "ANTHROPIC",
    "name": "claude-3-5-sonnet-20241022",
    "ctx_length": 200000,
    "max_tokens": 8192,
    "temperature": 0.7
  },
  "utility_model": {
    "provider": "ANTHROPIC", 
    "name": "claude-3-haiku-20240307",
    "ctx_length": 200000,
    "max_tokens": 4096,
    "temperature": 0.3
  },
  "embedding_model": {
    "provider": "HUGGINGFACE",
    "name": "sentence-transformers/all-MiniLM-L6-v2"
  }
}
```

### **Stap 4: RFC Uitschakelen**

In `python/helpers/settings.py` regel 945:

```python
"rfc_auto_docker": False,  # Was True, nu False
```

---

## 🚨 **Bekende Problemen & Oplossingen**

### **1. TypeError: unsupported operand type(s) for +: 'NoneType' and 'int'**

**Probleem:** Langchain_anthropic versie conflict bij token counting

**Oplossing 1 - Versie Downgrade:**
```bash
pip install langchain-anthropic==0.1.15
```

**Oplossing 2 - Monkey Patch:**
Maak `fix_anthropic.py`:

```python
import langchain_anthropic.chat_models as chat_models

original_create_usage_metadata = chat_models._create_usage_metadata

def patched_create_usage_metadata(anthropic_usage):
    try:
        return original_create_usage_metadata(anthropic_usage)
    except TypeError:
        # Fallback bij None token details
        return {
            "input_tokens": getattr(anthropic_usage, "input_tokens", 0),
            "output_tokens": getattr(anthropic_usage, "output_tokens", 0),
            "total_tokens": getattr(anthropic_usage, "input_tokens", 0) + getattr(anthropic_usage, "output_tokens", 0)
        }

chat_models._create_usage_metadata = patched_create_usage_metadata
```

Importeer dit in `models.py`:
```python
# Bovenaan models.py toevoegen
try:
    import fix_anthropic
except ImportError:
    pass
```

### **2. KeyError: 'openai' in settings**

**Oplossing:** Check alle JSON bestanden op provider names:
- `tmp/settings.json`
- `settings_claude.json` 
- `settings_claude_runtime.json`

Provider namen moeten UPPERCASE zijn: `"ANTHROPIC"`, `"HUGGINGFACE"`

### **3. Invalid x-api-key errors**

**Controles:**
1. `.env` bestand bestaat in root directory
2. API_KEY_ANTHROPIC correct ingesteld
3. Models.py heeft geen hardcoded keys in enums
4. `dotenv.load_dotenv()` wordt aangeroepen

### **4. RFC Connection Errors**

**Oplossing:**
```bash
# Voeg toe aan .env
RFC_AUTO_DOCKER=false
RFC_ENABLED=false
DISABLE_RFC=true
```

---

## 🎨 **Nederlandse Interface**

### **Automatische Vertaling Actief:**
- Alle buttons, labels en tooltips in het Nederlands
- Leon's branding (oranje #ff6600, blauw #0066cc)
- Custom welcome page: `webui/leon_welcome.html`
- Nederlandse developer badge

### **Interface Bestanden:**
- `webui/index.html` - Hoofdinterface
- `webui/index.css` - Styling met Leon's kleuren
- `webui/leon_welcome.html` - Custom welkomstpagina

---

## 🔧 **Development Scripts**

### **Setup Script: `setup_leon_env.py`**
```bash
python setup_leon_env.py
```

Controleert:
- Python versie
- Pip beschikbaarheid  
- Virtual environment
- Dependencies
- API keys
- Claude connectie

### **Start Scripts:**

**Normale Start:**
```bash
python run_ui.py --host 0.0.0.0 --port 50001
```

**Claude-Only Start:**
```bash
python run_claude_only.py
```

**Docker Start:**
```bash
./docker_claude_start.sh
```

---

## 📱 **Bot Configuratie**

### **Telegram Bot:**
- Token: `**********************************************`
- Config in `bot_config.json`
- Trading alerts geïntegreerd
- Auto-response systeem

### **WhatsApp Bot:**
- Business ID: `**********`
- Phone ID: `**********`
- Puppeteer-based automation
- CRM integratie klaar

### **Bot Features:**
- Real-time trading alerts
- Customer support automation
- Lead generation workflows
- Figma to code conversions
- Multi-language support

---

## 🔍 **Troubleshooting Commands**

### **Status Check:**
```bash
# Check of Agent Zero draait
ps aux | grep python | grep run_ui

# Check poorten
lsof -i :50001

# Check logs
tail -f logs/log_$(date +%Y%m%d)*.html
```

### **API Test:**
```bash
# Test Claude API
python -c "
from python.helpers import dotenv
dotenv.load_dotenv()
import os
print('API Key:', os.getenv('API_KEY_ANTHROPIC', 'NIET GEVONDEN'))
"
```

### **Settings Check:**
```bash
# Check settings
python -c "
from python.helpers.settings import get_default_settings
import json
settings = get_default_settings()
print(json.dumps(settings['chat_model'], indent=2))
"
```

---

## 🚀 **Performance Optimalisaties**

### **Claude Configuratie:**
- Context length: 200K tokens
- Temperature: 0.7 (chat), 0.3 (utility)
- Streaming enabled
- Memory systeem geoptimaliseerd

### **Memory Instellingen:**
- Subdirectories: "leon_main", "leon_dev"
- Auto-cleanup na 30 dagen
- Vector embeddings lokaal

### **Rate Limiting:**
- Anthropic: 50 requests/minute
- Token limits ingebouwd
- Graceful degradation

---

## 📚 **Extra Resources**

### **Documentatie:**
- `docs/architecture_overview.json` - Systeem architectuur
- `bot_config.json` - Complete bot setup
- `requirements_minimal.txt` - Minimale dependencies

### **Backup Configuraties:**
- `env_claude.txt` - Environment template
- `docker-compose-claude.yml` - Docker setup
- `settings_claude_runtime.json` - Runtime settings

---

## ⚠️ **Belangrijke Opmerkingen**

1. **API Limits:** Je hebt je Claude API limit bereikt tot 1 juli 2025
2. **RFC Disabled:** Voorkomt Docker conflicten, veilig om uit te laten
3. **Local AI Preferred:** Gebruik lokale models waar mogelijk
4. **Bot Tokens:** Bewaar veilig, vervang bij leaks
5. **Regular Updates:** Check langchain_anthropic versie regelmatig

---

## 🎯 **Leon's Specialisaties Ready**

✅ **Telegram/WhatsApp Bots** - Complete configuratie klaar  
✅ **Trading Bots** - Alert systemen geïntegreerd  
✅ **CRM Systemen** - Database connecties voorbereid  
✅ **Figma to Code** - Workflow automation actief  
✅ **Nederlandse AI** - Interface volledig gelokaliseerd  
✅ **Direct Bruikbare Code** - Minimale comments, maximum functionaliteit  
✅ **3 Opties Principe** - Altijd meerdere oplossingen aanbieden  

---

**🎉 Agent Zero is klaar voor Nederlandse AI-ontwikkeling! 🇳🇱** 