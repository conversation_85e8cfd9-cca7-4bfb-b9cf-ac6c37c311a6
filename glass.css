/* Achtergrond blur voor volledige editor */
.monaco-workbench, .editor-instance, .part.sidebar, .part.editor, .panel {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background-color: rgba(20, 20, 30, 0.35) !important;
  box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.1);
}

/* Glowing tab labels */
.tab-label {
  color: #ccfaff !important;
  text-shadow: 0 0 6px #00ffff;
}

/* Editor tekst glow */
.monaco-editor .view-lines {
  text-shadow: 0 0 3px #00ffff66;
}

/* Actieve regel lichte achtergrond */
.monaco-editor .current-line {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Optioneel: terminal transparant + glow */
.terminal {
  background-color: rgba(0, 0, 0, 0.3) !important;
  color: #00ffff !important;
}
.monaco-workbench, .editor-instance, .part.sidebar, .panel, .chat-view, .right {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background-color: rgba(20, 20, 30, 0.35) !important;
  box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.tab-label {
  color: #ccfaff !important;
  text-shadow: 0 0 6px #00ffff;
}

.monaco-editor .view-lines {
  text-shadow: 0 0 3px #00ffff66;
}
