# Maak docker-compose.yml in je root
version: '3.8'

services:
  agent-zero:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: leon-agent-zero
    restart: unless-stopped  # 24/7 restart policy
    ports:
      - "50001:50001"  # Web UI
      - "50002:22"     # SSH (optioneel)
    environment:
      - PORT=50001
      - PYTHONUNBUFFERED=1
      - DOCKER_ENV=true  # <PERSON>'s combined service flag
      - API_KEY_OPENROUTER=${API_KEY_OPENROUTER}
      - API_KEY_DEEPSEEK=${API_KEY_DEEPSEEK}
      - MEXC_API_KEY=${MEXC_API_KEY}
      - MEXC_API_SECRET=${MEXC_API_SECRET}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_TRADING_PASSWORD=${TELEGRAM_TRADING_PASSWORD}
    volumes:
      - ./logs:/app/logs
      - ./memory:/app/memory
      - ./knowledge:/app/knowledge
      - ./tmp:/app/tmp
      - agent-zero-data:/app/data
    networks:
      - leon-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optioneel: Thomas TTS Service
  thomas-tts:
    build:
      context: ./agent_zero_service
      dockerfile: Dockerfile
    container_name: leon-thomas-tts
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - thomas-data:/app/data
    networks:
      - leon-network
    depends_on:
      - agent-zero

volumes:
  agent-zero-data:
  thomas-data:

networks:
  leon-network:
    driver: bridge

# Optional: Redis for caching (uncomment if needed)
# redis:
#   image: redis:7-alpine
#   container_name: leon-redis
#   restart: unless-stopped
#   ports:
#     - "6379:6379"
#   volumes:
#     - redis_data:/data
#   networks:
#     - agent-zero-network

# Optional: PostgreSQL for data storage (uncomment if needed)
# postgres:
#   image: postgres:15-alpine
#   container_name: leon-postgres
#   restart: unless-stopped
#   environment:
#     POSTGRES_DB: agent_zero
#     POSTGRES_USER: leon
#     POSTGRES_PASSWORD: leon2024
#   ports:
#     - "5432:5432"
#   volumes:
#     - postgres_data:/var/lib/postgresql/data
#   networks:
#     - agent-zero-network

# Uncomment if using optional services
# redis_data:
# postgres_data: