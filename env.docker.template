# Agent Zero Docker Environment - <PERSON>'s <PERSON> Setup
# Kopieer dit bestand naar .env en vul je API keys in

# Claude API (Required)
CLAUDE_API_KEY=your_claude_api_key_here

# Optional: Other AI Providers
GROQ_API_KEY=
PERPLEXITY_API_KEY=
GOOGLE_API_KEY=
MISTRAL_API_KEY=
ANTHROPIC_API_KEY=${CLAUDE_API_KEY}

# Bot Integration (voor Telegram/WhatsApp)
TELEGRAM_BOT_TOKEN=
WHATSAPP_API_KEY=

# Web UI Settings
WEB_UI_PORT=50001
USE_CLOUDFLARE=false

# Performance
TOKENIZERS_PARALLELISM=true
PYDEVD_DISABLE_FILE_VALIDATION=1 