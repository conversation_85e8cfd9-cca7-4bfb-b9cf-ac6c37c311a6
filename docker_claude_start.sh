#!/bin/bash
# Agent Zero - Leon's Docker Claude Setup Script
# Geen dependencies installeren meer nodig!

set -e

echo "🐳 Agent Zero Docker Claude Setup - Leon's Edition"
echo "=================================================="

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is niet actief. Start Docker Desktop eerst."
    exit 1
fi

# Create data directory if it doesn't exist
DATA_DIR="${HOME}/agent-zero-data"
if [ ! -d "$DATA_DIR" ]; then
    echo "📁 Aanmaken data directory: $DATA_DIR"
    mkdir -p "$DATA_DIR"
fi

# Create .env file for Claude API if it doesn't exist
ENV_FILE="$DATA_DIR/.env"
if [ ! -f "$ENV_FILE" ]; then
    echo "⚙️  Aanmaken Claude API configuratie..."
    cat > "$ENV_FILE" << EOF
# Leon's Claude API Configuration
API_KEY_ANTHROPIC=your_claude_api_key_here

# Andere API Keys (optioneel)
API_KEY_OPENAI=
API_KEY_GROQ=
API_KEY_PERPLEXITY=
API_KEY_GOOGLE=
API_KEY_MISTRAL=
API_KEY_OPENROUTER=

# Web UI Settings
WEB_UI_PORT=50001
USE_CLOUDFLARE=false

# Performance Settings  
TOKENIZERS_PARALLELISM=true
PYDEVD_DISABLE_FILE_VALIDATION=1
EOF
    echo "✅ .env bestand aangemaakt in $ENV_FILE"
    echo "⚠️  BELANGRIJK: Voeg je Claude API key toe aan $ENV_FILE"
fi

# Copy current bot config and architecture docs to data directory
if [ -f "bot_config.json" ]; then
    cp bot_config.json "$DATA_DIR/"
    echo "✅ Bot configuratie gekopieerd"
fi

if [ -f "docs/architecture_overview.json" ]; then
    mkdir -p "$DATA_DIR/docs"
    cp docs/architecture_overview.json "$DATA_DIR/docs/"
    echo "✅ Architectuur documentatie gekopieerd"
fi

# Pull latest Agent Zero Docker image
echo "📥 Downloaden nieuwste Agent Zero Docker image..."
docker pull frdel/agent-zero-run:latest

# Stop existing container if running
if docker ps -q -f name=agent-zero-claude >/dev/null 2>&1; then
    echo "🛑 Stoppen bestaande container..."
    docker stop agent-zero-claude >/dev/null 2>&1
    docker rm agent-zero-claude >/dev/null 2>&1
fi

# Start Agent Zero container with Claude configuration
echo "🚀 Starten Agent Zero met Claude configuratie..."
docker run -d \
    --name agent-zero-claude \
    -p 50001:80 \
    -v "$DATA_DIR:/a0" \
    --restart unless-stopped \
    frdel/agent-zero-run:latest

# Wait for container to start
echo "⏳ Wachten tot container opstart..."
sleep 10

# Check if container is running
if docker ps -q -f name=agent-zero-claude >/dev/null 2>&1; then
    echo ""
    echo "🎉 Agent Zero succesvol gestart!"
    echo ""
    echo "📋 Details:"
    echo "   🌐 Web Interface: http://localhost:50001"
    echo "   📁 Data Directory: $DATA_DIR"
    echo "   🔑 API Keys: $ENV_FILE"
    echo "   🤖 Bot Config: $DATA_DIR/bot_config.json"
    echo ""
    echo "🔧 Volgende stappen:"
    echo "   1. Voeg je Claude API key toe aan: $ENV_FILE"
    echo "   2. Open http://localhost:50001 in je browser"
    echo "   3. Ga naar Settings → Chat Model → Selecteer 'anthropic' als provider"
    echo "   4. Model naam: claude-3-5-sonnet-20241022"
    echo ""
    echo "🛠️  Handige commando's:"
    echo "   docker logs agent-zero-claude      # Bekijk logs"
    echo "   docker stop agent-zero-claude      # Stop container"
    echo "   docker start agent-zero-claude     # Start container"
    echo "   docker restart agent-zero-claude   # Herstart container"
    echo ""
else
    echo "❌ Er ging iets mis. Check de logs:"
    docker logs agent-zero-claude
    exit 1
fi 