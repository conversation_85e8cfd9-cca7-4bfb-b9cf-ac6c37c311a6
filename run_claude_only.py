#!/usr/bin/env python3
"""
Agent Zero - Claude Only Version
Direct startup script met alleen Claude API support
"""

import json
import os
import subprocess
import sys
from pathlib import Path


def setup_environment():
    """Setup environment voor Claude"""
    print("🔧 Setting up Claude environment...")

    # Environment variables voor Claude
    os.environ["API_KEY_ANTHROPIC"] = os.getenv("API_KEY_ANTHROPIC", "")
    os.environ["ANTHROPIC_API_KEY"] = os.getenv("API_KEY_ANTHROPIC", "")

    # Disable RFC completely
    os.environ["RFC_AUTO_DOCKER"] = "false"
    os.environ["RFC_ENABLED"] = "false"
    os.environ["DISABLE_RFC"] = "true"

    # Andere settings
    os.environ["PYTHONPATH"] = str(Path.cwd())

    print("✅ Environment setup complete")


def create_claude_settings():
    """Create settings file optimized for <PERSON>"""
    settings = {
        "chat_model_provider": "ANTHROPIC",  # Fixed: use uppercase ANTHROPIC to match ModelProvider enum
        "chat_model": "claude-3-5-sonnet-20241022",
        "chat_model_ctx_length": 200000,
        "chat_model_max_tokens": 8192,
        "chat_model_temperature": 0.7,
        "utility_model_provider": "ANTHROPIC",
        "utility_model": "claude-3-haiku-20240307",
        "utility_model_ctx_length": 200000,
        "utility_model_max_tokens": 4096,
        "utility_model_temperature": 0.3,
        "embedding_model_provider": "OPENAI",
        "embedding_model": "text-embedding-3-small",
        "embedding_model_ctx_length": 8191,
        "speech_to_text_provider": "OPENAI",
        "speech_to_text_model": "whisper-1",
        "agent_prompts_subdir": "default",
        "agent_memory_subdir": "main",
        "agent_knowledge_subdir": "main",
        "web_ui_port": 50001,
        "web_ui_host": "0.0.0.0",
        # RFC disabled
        "rfc_auto_docker": False,
        "rfc_enabled": False,
        # Claude optimizations
        "ctx_window_ratio": 0.7,
        "streaming": True,
    }

    with open("settings_claude_runtime.json", "w") as f:
        json.dump(settings, f, indent=2)

    print("✅ Claude settings created")


def check_api_key():
    """Check Claude API key"""
    api_key = os.getenv("API_KEY_ANTHROPIC")
    if not api_key or api_key == "":
        print("❌ FOUT: API_KEY_ANTHROPIC niet gevonden!")
        print("Voeg toe aan .env bestand:")
        print("API_KEY_ANTHROPIC=sk-ant-...")
        return False

    if api_key.startswith("${") and api_key.endswith("}"):
        print("❌ FOUT: API key heeft nog ${} format")
        print("Verwijder de ${} en gebruik alleen: sk-ant-...")
        return False

    print(f"✅ Claude API key gevonden: {api_key[:20]}...")
    return True


def start_claude_agent():
    """Start Agent Zero met Claude via run_ui.py"""
    print("🚀 Starting Agent Zero met Claude...")

    # Direct run_ui.py gebruiken met environment variables
    cmd = [sys.executable, "run_ui.py", "--host", "0.0.0.0", "--port", "50001"]

    print(f"Running: {' '.join(cmd)}")
    subprocess.run(cmd, env=os.environ)


def main():
    """Main startup functie"""
    print("🤖 Agent Zero - Claude Only Version")
    print("=" * 50)

    # Setup
    setup_environment()
    create_claude_settings()

    # Check API key
    if not check_api_key():
        return

    # Start
    start_claude_agent()


if __name__ == "__main__":
    main()
