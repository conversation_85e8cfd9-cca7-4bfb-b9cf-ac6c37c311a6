#!/bin/bash
# <PERSON>'s Agent Zero - Docker Build & Deploy Script
# Easy deployment voor jouw AI Development Hub

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Leon's banner
echo -e "${PURPLE}"
echo "🚀 Leon's Agent Zero - Docker Deployment"
echo "========================================="
echo "🤖 DeepSeek AI + Trading Bots + Telegram"
echo "🇳🇱 Dutch AI Development Hub"
echo "========================================="
echo -e "${NC}"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker is niet actief. Start Docker Desktop eerst.${NC}"
    exit 1
fi

# Check for .env file
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  Geen .env bestand gevonden. Kopiëren van template...${NC}"
    cp .env.template .env
    echo -e "${YELLOW}📝 Bewerk .env en voeg je API keys toe!${NC}"
    echo -e "${BLUE}💡 Minimaal nodig: API_KEY_DEEPSEEK${NC}"
fi

# Create necessary directories
echo -e "${BLUE}📁 Aanmaken directories...${NC}"
mkdir -p data logs memory knowledge tmp

# Function to build image
build_image() {
    echo -e "${BLUE}🔨 Building Leon's Agent Zero Docker image...${NC}"
    docker build -t leon/agent-zero:latest .
    echo -e "${GREEN}✅ Docker image gebouwd!${NC}"
}

# Function to start containers
start_containers() {
    echo -e "${BLUE}🚀 Starting containers...${NC}"
    docker-compose up -d
    echo -e "${GREEN}✅ Containers gestart!${NC}"
}

# Function to show status
show_status() {
    echo -e "${BLUE}📊 Container status:${NC}"
    docker-compose ps
    
    echo -e "\n${GREEN}🌐 Toegang:${NC}"
    echo -e "${BLUE}Web UI:${NC} http://localhost:50001"
    echo -e "${BLUE}Direct Port:${NC} http://localhost:50002"
    echo -e "${BLUE}SSH:${NC} ssh leon@localhost -p 50022 (wachtwoord: leon2024)"
    
    echo -e "\n${GREEN}🔧 Logs bekijken:${NC}"
    echo -e "${BLUE}Agent Zero:${NC} docker-compose logs -f agent-zero"
    echo -e "${BLUE}Alle services:${NC} docker-compose logs -f"
}

# Function to stop containers
stop_containers() {
    echo -e "${YELLOW}🛑 Stopping containers...${NC}"
    docker-compose down
    echo -e "${GREEN}✅ Containers gestopt!${NC}"
}

# Function to restart containers
restart_containers() {
    echo -e "${YELLOW}🔄 Restarting containers...${NC}"
    docker-compose restart
    echo -e "${GREEN}✅ Containers herstart!${NC}"
}

# Function to show logs
show_logs() {
    echo -e "${BLUE}📋 Toon logs...${NC}"
    docker-compose logs -f
}

# Function to clean up
cleanup() {
    echo -e "${YELLOW}🧹 Opruimen containers en images...${NC}"
    docker-compose down -v
    docker rmi leon/agent-zero:latest 2>/dev/null || true
    echo -e "${GREEN}✅ Cleanup voltooid!${NC}"
}

# Main menu
case "${1:-menu}" in
    "build")
        build_image
        ;;
    "start")
        start_containers
        ;;
    "stop")
        stop_containers
        ;;
    "restart")
        restart_containers
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "deploy")
        build_image
        start_containers
        sleep 10
        show_status
        ;;
    "clean")
        cleanup
        ;;
    "menu"|*)
        echo -e "${GREEN}📋 Beschikbare commando's:${NC}"
        echo ""
        echo -e "${BLUE}./docker-build.sh deploy${NC}   - Build en start alles"
        echo -e "${BLUE}./docker-build.sh build${NC}    - Build alleen de image"
        echo -e "${BLUE}./docker-build.sh start${NC}    - Start containers"
        echo -e "${BLUE}./docker-build.sh stop${NC}     - Stop containers"
        echo -e "${BLUE}./docker-build.sh restart${NC}  - Herstart containers"
        echo -e "${BLUE}./docker-build.sh status${NC}   - Toon status"
        echo -e "${BLUE}./docker-build.sh logs${NC}     - Toon logs"
        echo -e "${BLUE}./docker-build.sh clean${NC}    - Cleanup alles"
        echo ""
        echo -e "${YELLOW}💡 Voor eerste keer: ./docker-build.sh deploy${NC}"
        ;;
esac

echo -e "${PURPLE}"
echo "========================================="
echo "🎯 Leon's Agent Zero - Klaar voor gebruik!"
echo "========================================="
echo -e "${NC}" 