# <PERSON>'s Agent Zero - Simple Docker Setup
FROM python:3.13-slim-bookworm

LABEL maintainer="Leon - Dutch AI Developer"
LABEL description="Agent Zero Minimal - DeepSeek + Trading + Telegram"

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
  curl \
  wget \
  git \
  build-essential \
  && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /app

# Copy application code first for requirements
COPY . .

# Install all requirements
RUN pip install --no-cache-dir --upgrade pip && \
  pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/tmp /app/logs /app/memory /app/knowledge

# Copy Leon's DeepSeek config
COPY settings_deepseek.json /app/tmp/settings.json
COPY .env* /app/

# Create startup script
RUN echo '#!/bin/bash\n\
  set -e\n\
  echo "🚀 Starting Leon'\''s Agent Zero - Simple Setup"\n\
  echo "🤖 DeepSeek AI Models: ACTIVE"\n\
  echo "🌐 Web UI: http://localhost:50001"\n\
  echo "Starting Agent Zero..."\n\
  cd /app\n\
  python run_ui.py --host 0.0.0.0 --port 50001\n\
  ' > /app/docker-start.sh && chmod +x /app/docker-start.sh

# Add health check endpoint
RUN echo 'from flask import Flask\n\
  app = Flask(__name__)\n\
  @app.route("/health")\n\
  def health():\n\
  return "healthy"\n\
  if __name__ == "__main__":\n\
  app.run(host="0.0.0.0", port=8080)\n\
  ' > /app/health_check.py

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:50001 || exit 1

# Expose port (Railway uses PORT env var)
EXPOSE 50001

# Update start script for cloud
RUN echo '#!/bin/bash\n\
  set -e\n\
  echo "🚀 Starting Leon'\''s Agent Zero - Cloud Setup"\n\
  echo "🤖 DeepSeek AI Models: ACTIVE"\n\
  echo "🌐 Web UI: https://$RAILWAY_PUBLIC_DOMAIN (port ${PORT:-50001})"\n\
  echo "Starting Agent Zero..."\n\
  cd /app\n\
  python run_ui.py --host 0.0.0.0 --port ${PORT:-50001}\n\
  ' > /app/docker-start.sh && chmod +x /app/docker-start.sh

# Start command
CMD ["/app/docker-start.sh"] 