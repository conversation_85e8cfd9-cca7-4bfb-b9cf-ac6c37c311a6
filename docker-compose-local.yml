version: "3.8"

services:
  agent-zero:
    image: frdel/agent-zero-run:latest
    container_name: leon-agent-zero-local
    env_file:
      - .env
    ports:
      - "50001:80"
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./tmp:/app/tmp
      - ./memory:/app/memory
      - ./knowledge:/app/knowledge
      # <PERSON>'s configuratie - Force OpenRouter/DeepSeek
      - ./tmp/settings_docker.json:/a0/tmp/settings.json
      - ./.env:/a0/.env
      # <PERSON>'s Anthropic patch
      - ./python/helpers/anthropic_patch.py:/a0/python/helpers/anthropic_patch.py
      - ./agent.py:/a0/agent.py
      # <PERSON>'s settings fix
      - ./python/helpers/settings.py:/a0/python/helpers/settings.py
    environment:
      - DOCKER_ENV=true
      - PYTHONUNBUFFERED=1
      # <PERSON>'s API Keys - Working OpenAI Setup
      - API_KEY_OPENAI=********************************************************************************************************************************************************************
      # Trading & Telegram
      - MEXC_API_KEY=mx0vglmGyrf9LW7tY9
      - MEXC_API_SECRET=b5bb5493128b483c9ebe199d013042c1
      - TELEGRAM_BOT_TOKEN=**********:AAH_4ILh_DFX-jmMZQjK0Z3b6X_BZf0Ddts
      - TELEGRAM_TRADING_PASSWORD=Fatima23
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:80/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
