{"leon_master_ai_stack": {"services": {"thomas_tts": {"url": "http://localhost:8080", "endpoint": "/thomas/synthesize", "health_endpoint": "/thomas/health", "timeout": 30, "max_retries": 3, "rate_limit": {"requests_per_minute": 60, "burst_limit": 10}}, "ollama": {"url": "http://localhost:11434", "endpoint": "/api/generate", "models": {"primary": "llama3.2:latest", "fallback": "llama3.1:8b", "chat": "llama3.2:3b"}, "timeout": 60, "max_retries": 2}, "chromadb": {"url": "http://localhost:8000", "collection": "leon_knowledge", "timeout": 15, "max_results": 10}, "agent_zero": {"url": "http://localhost:50001", "timeout": 30}, "whisper": {"model": "base", "language": "nl", "ffmpeg_path": "/opt/homebrew/bin/ffmpeg", "temp_dir": "/tmp/leon_audio"}}, "commands": ["thomas_speak", "ai_chat", "voice_command", "knowledge_query", "multi_agent_discussion"], "pipelines": {"voice": ["whisper_STT", "intent_routing", "ollama_LLM", "thomas_<PERSON>"], "chat": ["ollama_LLM", "conversation_history", "thomas_<PERSON>"], "knowledge": ["chromadb_query", "contextual_LLM"]}, "utilities": ["emotion_detection", "text_optimisation", "service_healthcheck"], "security": {"log_level": "INFO", "sensitive_patterns": ["api_key", "password", "token", "secret"], "max_prompt_log_length": 100, "enable_rate_limiting": true}, "performance": {"enable_async_gather": true, "max_concurrent_requests": 5, "cache_responses": true, "cache_ttl_seconds": 300}, "development": {"debug_mode": true, "extensive_logging": true, "test_mode": false}}}