# Leon's Agent Zero - Docker Ignore
# Exclude heavy development files

# Version control
.git/
.gitignore

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.venv/
agent-zero-env/

# Node modules
node_modules/
npm-debug.log*

# Logs
logs/
*.log

# Cache en temp
tmp/
.cache/
*.tmp
*.temp

# Environment files - BELANGRIJK!
.env
.env.*

# Development
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker files (we only need the main Dockerfile)
docker/
Dockerfile.simple

# External dependencies (groot!)
external/
agent_zero_service/external/

API_KEY_OPENROUTER=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
API_KEY_OPENAI=******************************************************************************************************************************************************************** 