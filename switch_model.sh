#!/bin/bash
# <PERSON>'s Model Switcher - Switch between AI configurations
# Easy switching tussen verschillende AI models

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}"
echo "🤖 <PERSON>'s AI Model Switcher"
echo "============================="
echo "🔄 Switch between AI configurations"
echo "============================="
echo -e "${NC}"

# Function to stop Agent Zero
stop_agent() {
    echo -e "${YELLOW}🛑 Stopping Agent Zero...${NC}"
    pkill -f "python run_ui.py" || true
    sleep 2
}

# Function to start Agent Zero
start_agent() {
    echo -e "${BLUE}🚀 Starting Agent Zero...${NC}"
    source agent-zero-env/bin/activate
    python run_ui.py --host 0.0.0.0 --port 50001 &
    sleep 5
    echo -e "${GREEN}✅ Agent Zero started: http://localhost:50001${NC}"
}

# Function to switch model
switch_model() {
    local config_file=$1
    local model_name=$2
    
    echo -e "${BLUE}🔄 Switching to ${model_name}...${NC}"
    
    if [ ! -f "$config_file" ]; then
        echo -e "${RED}❌ Configuration file $config_file not found!${NC}"
        exit 1
    fi
    
    # Backup current settings
    cp tmp/settings.json tmp/settings_backup_$(date +%Y%m%d_%H%M%S).json
    
    # Copy new settings
    cp "$config_file" tmp/settings.json
    
    echo -e "${GREEN}✅ Switched to ${model_name}${NC}"
    echo -e "${BLUE}📋 Active configuration: $config_file${NC}"
}

# Menu
case "${1:-menu}" in
    "r1"|"deepseek-r1")
        stop_agent
        switch_model "settings_deepseek_r1.json" "DeepSeek R1 (Latest)"
        start_agent
        ;;
    "regular"|"deepseek")
        stop_agent
        switch_model "settings_openrouter_deepseek.json" "DeepSeek Chat/Coder"
        start_agent
        ;;
    "claude")
        stop_agent
        switch_model "settings_claude.json" "Claude (Anthropic)"
        start_agent
        ;;
    "working"|"backup")
        stop_agent
        switch_model "settings_working_backup.json" "Last Working Configuration"
        start_agent
        ;;
    "status")
        echo -e "${BLUE}📊 Current Configuration:${NC}"
        if [ -f tmp/settings.json ]; then
            echo -e "${GREEN}Chat Model:${NC} $(grep -o '"chat_model_name": "[^"]*"' tmp/settings.json | cut -d'"' -f4)"
            echo -e "${GREEN}Provider:${NC} $(grep -o '"chat_model_provider": "[^"]*"' tmp/settings.json | cut -d'"' -f4)"
            echo -e "${GREEN}Base URL:${NC} $(grep -o '"base_url": "[^"]*"' tmp/settings.json | cut -d'"' -f4 || echo "Default")"
        else
            echo -e "${RED}❌ No configuration found${NC}"
        fi
        ;;
    "menu"|*)
        echo -e "${GREEN}🎯 Available Models:${NC}"
        echo ""
        echo -e "${BLUE}./switch_model.sh r1${NC}        - DeepSeek R1 (Latest reasoning model)"
        echo -e "${BLUE}./switch_model.sh regular${NC}    - DeepSeek Chat/Coder (Stable)"
        echo -e "${BLUE}./switch_model.sh claude${NC}     - Claude (Anthropic)"
        echo -e "${BLUE}./switch_model.sh working${NC}    - Last working configuration"
        echo ""
        echo -e "${BLUE}./switch_model.sh status${NC}     - Show current configuration"
        echo ""
        echo -e "${YELLOW}💡 Recommended: Start with 'regular' for stability${NC}"
        echo -e "${YELLOW}🧠 Try 'r1' for advanced reasoning tasks${NC}"
        ;;
esac

echo -e "${PURPLE}"
echo "============================="
echo "🎯 Leon's AI Ready for Action!"
echo "============================="
echo -e "${NC}" 