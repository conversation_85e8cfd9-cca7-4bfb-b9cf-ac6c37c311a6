#!/bin/bash
# <PERSON>'s Cloud Deploy Script
# Quick deployment naar Railway cloud platform

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}"
echo "🚀 <PERSON>'s Agent Zero - Cloud Deploy"
echo "===================================="
echo "🌍 Deploy naar Railway (24/7 online)"
echo "===================================="
echo -e "${NC}"

# Check if git repo exists
if [ ! -d ".git" ]; then
    echo -e "${YELLOW}📁 Initializing git repository...${NC}"
    git init
    git add .
    git commit -m "Initial commit: <PERSON>'s Agent Zero"
fi

# Check for Railway CLI
if ! command -v railway &> /dev/null; then
    echo -e "${BLUE}📦 Installing Railway CLI...${NC}"
    npm install -g @railway/cli
fi

# Check current git status
echo -e "${BLUE}📋 Current git status:${NC}"
git status --short

# Ask user to confirm
echo -e "${YELLOW}⚠️  Ready to deploy to Railway cloud?${NC}"
echo -e "${BLUE}💡 This will:${NC}"
echo "   - Commit current changes"
echo "   - Push to GitHub (if configured)"
echo "   - Deploy to Railway cloud"
echo "   - Make Agent Zero 24/7 accessible"
echo ""
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}❌ Deployment cancelled${NC}"
    exit 1
fi

# Commit changes
echo -e "${BLUE}💾 Committing changes...${NC}"
git add .
git commit -m "Leon's Agent Zero - Cloud deployment ready" || echo "No changes to commit"

# Check if Railway is logged in
echo -e "${BLUE}🔐 Checking Railway authentication...${NC}"
if ! railway whoami &> /dev/null; then
    echo -e "${YELLOW}🔑 Please login to Railway:${NC}"
    railway login
fi

# Deploy to Railway
echo -e "${BLUE}🚀 Deploying to Railway...${NC}"
railway up

# Get the deployment URL
echo -e "${GREEN}✅ Deployment started!${NC}"
echo -e "${BLUE}📋 Getting deployment info...${NC}"

# Wait a moment for deployment to initialize
sleep 5

# Show deployment status
railway status

echo -e "${GREEN}"
echo "🎉 Leon's Agent Zero - Cloud Deployment Complete!"
echo "================================================="
echo "🌍 Your AI Hub is now 24/7 online!"
echo ""
echo "📱 Next steps:"
echo "1. Check Railway dashboard for your URL"
echo "2. Add environment variables in Railway settings:"
echo "   - API_KEY_OPENROUTER"
echo "   - API_KEY_ANTHROPIC"
echo "   - MEXC_API_KEY (optional)"
echo "   - TELEGRAM_BOT_TOKEN (optional)"
echo ""
echo "🔧 Monitor logs: railway logs"
echo "⚙️  Dashboard: railway open"
echo "================================================="
echo -e "${NC}" 