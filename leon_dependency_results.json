{"services": {"thomas_tts": {"status": "failed", "error": "Cannot connect to host localhost:8080 ssl:default [Connect call failed ('127.0.0.1', 8080)]"}, "chromadb": {"status": "failed", "error": "Cannot connect to host localhost:8000 ssl:default [Connect call failed ('127.0.0.1', 8000)]"}, "ollama": {"status": "healthy", "url": "http://localhost:11434", "response_code": 200}, "ollama_primary": {"status": "available", "model": "llama3.2:latest"}, "ollama_fallback": {"status": "available", "model": "llama3.1:8b"}, "ollama_chat": {"status": "available", "model": "llama3.2:3b"}, "agent_zero": {"status": "healthy", "url": "http://localhost:50001", "response_code": 200}}, "dependencies": {"aiohttp": {"status": "ok"}, "requests": {"status": "ok"}, "whisper": {"status": "ok"}, "gtts": {"status": "ok"}, "pygame": {"status": "ok"}, "nemo": {"status": "missing", "optional": true}, "chromadb": {"status": "missing", "optional": true}}, "system": {"ffmpeg": {"status": "ok", "version": "ffmpeg version 7.1.1 Copyright (c) 2000-2025 the FFmpeg developers"}, "say": {"status": "ok", "version": "<PERSON>              en_US    # Hello! My name is <PERSON>."}, "python": {"status": "ok", "version": "Python 3.13.5"}, "ffmpeg_whisper": {"status": "ok", "path": "/opt/homebrew/bin/ffmpeg"}, "audio_temp_dir": {"status": "ok", "path": "/tmp/leon_audio"}}, "security": {"sensitive_patterns": {"status": "configured", "value": ["api_key", "password", "token", "secret"]}, "max_prompt_log_length": {"status": "configured", "value": 100}, "enable_rate_limiting": {"status": "configured", "value": true}}, "overall_status": "degraded"}