#!/usr/bin/env python3
"""
Agent <PERSON> - Leon's <PERSON>t
Quick launcher met automatische Claude API configuratie
"""

import os
import subprocess
import sys
from pathlib import Path


def print_status(message, status="INFO"):
    """Print status met kleurcodering"""
    colors = {
        "OK": "\033[92m✓\033[0m",
        "ERROR": "\033[91m✗\033[0m",
        "WARNING": "\033[93m⚠\033[0m",
        "INFO": "\033[94mℹ\033[0m",
    }
    print(f"{colors.get(status, '•')} {message}")


def check_env_file():
    """Controleer of .env bestand bestaat en Claude API key bevat"""
    env_path = Path(".env")
    if not env_path.exists():
        print_status(".env bestand niet gevonden", "ERROR")
        return False

    with open(env_path, "r") as f:
        content = f.read()
        if (
            "API_KEY_ANTHROPIC=" in content
            and "your_claude_api_key_here" not in content
        ) or ("API_KEY_DEEPSEEK=" in content and "sk-or-v1-" in content):
            print_status("AI model API key configuratie gevonden (.env)", "OK")
            print_status("→ DeepSeek key gevonden en klaar voor gebruik", "INFO")
            return True
        else:
            print_status("Geen geldige Claude of DeepSeek API key in .env", "WARNING")
            return False


def activate_and_start():
    """Activeer virtual environment en start Agent Zero"""
    venv_path = Path("agent-zero-env")
    if not venv_path.exists():
        print_status("Virtual environment niet gevonden", "ERROR")
        return False

    # Activate virtual environment en start Agent Zero
    if sys.platform == "win32":
        activate_script = venv_path / "Scripts" / "activate.bat"
        python_path = venv_path / "Scripts" / "python.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        python_path = venv_path / "bin" / "python"

    if not python_path.exists():
        print_status(
            f"Python niet gevonden in virtual environment: {python_path}", "ERROR"
        )
        return False

    print_status("Virtual environment geactiveerd", "OK")

    # Start Agent Zero UI
    print_status("Agent Zero starten met Claude configuratie...", "INFO")
    cmd = [str(python_path), "run_ui.py", "--host", "0.0.0.0", "--port", "50001"]

    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print_status(f"Fout bij starten Agent Zero: {e}", "ERROR")
        return False
    except KeyboardInterrupt:
        print_status("Agent Zero gestopt door gebruiker", "INFO")
        return True

    return True


def main():
    """Hoofdfunctie"""
    print("=" * 60)
    print("🤖 Agent Zero - Leon's Claude Startup Script")
    print("=" * 60)

    # Check current directory
    if not Path("run_ui.py").exists():
        print_status("Niet in Agent Zero directory - ga naar de juiste map", "ERROR")
        sys.exit(1)

    # Check .env configuration
    env_ok = check_env_file()
    if not env_ok:
        print_status(
            "Configureer eerst je Claude of DeepSeek API key in .env bestand:", "INFO"
        )
        print_status("API_KEY_ANTHROPIC=...", "INFO")
        print_status("OF", "INFO")
        print_status("API_KEY_DEEPSEEK=sk-or-v1-...", "INFO")

    # Start Agent Zero
    print_status("Agent Zero wordt gestart op http://localhost:50001", "INFO")
    print_status("Druk Ctrl+C om te stoppen", "INFO")

    success = activate_and_start()

    if success:
        print_status("Agent Zero succesvol gestart! 🚀", "OK")
        print_status("Bot configuratie: bot_config.json", "INFO")
        print_status("Architectuur overzicht: docs/architecture_overview.json", "INFO")
    else:
        print_status("Er ging iets mis bij het starten", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
