version: '3.8'

services:
  agent-zero-claude:
    image: frdel/agent-zero-run:latest
    container_name: agent-zero-claude
    restart: unless-stopped
    ports:
      - "50001:80"
    volumes:
      # Mount data directory for persistence
      - ${HOME}/agent-zero-data:/a0
      # Optional: mount current directory configs
      - ./bot_config.json:/a0/bot_config.json:ro
      - ./docs/architecture_overview.json:/a0/docs/architecture_overview.json:ro
    environment:
      # Claude API Configuration
      - API_KEY_ANTHROPIC=${CLAUDE_API_KEY:-your_claude_api_key_here}
      
      # Optional: Other API Keys
      - API_KEY_OPENAI=${OPENAI_API_KEY:-}
      - API_KEY_GROQ=${GROQ_API_KEY:-}
      - API_KEY_PERPLEXITY=${PERPLEXITY_API_KEY:-}
      
      # Web UI Settings
      - WEB_UI_PORT=50001
      - USE_CLOUDFLARE=false
      
      # Performance Settings
      - TOKENIZERS_PARALLELISM=true
      - PYDEVD_DISABLE_FILE_VALIDATION=1
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    labels:
      - "com.agent-zero.description=Agent Zero with Claude API - Leon's Edition"
      - "com.agent-zero.version=latest"
      - "com.agent-zero.api=claude"

networks:
  default:
    name: agent-zero-network 