[build]
builder = "NIXPACKS"
buildCommand = "echo 'Building Leon Agent Zero'"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[project]
id = "923b1470-4bc2-46e7-b2a2-35345a6d0e21"
name = "leon-agent-zero"

[environment]
PORT = "50001"
PYTHONUNBUFFERED = "1"
NODE_ENV = "production"
RAILWAY_ENVIRONMENT = "production"

[networking]
domain = "agent-zero-production-923b1470.up.railway.app"
