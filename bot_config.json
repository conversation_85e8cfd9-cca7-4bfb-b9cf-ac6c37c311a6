{"bot_info": {"name": "Leon's Agent <PERSON>", "description": "🇳🇱 Nederlandse AI-ontwikkelaar specialist voor Telegram/WhatsApp bots, trading systemen en workflow automation", "version": "1.0.0-leon", "developer": "Leon - Nederlandse AI Developer", "language": "nl", "timezone": "Europe/Amsterdam"}, "telegram_bot": {"enabled": true, "bot_token": "**********************************************", "webhook_url": "https://agent-zero-production-923b1470.up.railway.app/telegram-webhook", "commands": [{"command": "/start", "description": "🚀 <PERSON> <PERSON><PERSON><PERSON><PERSON> met <PERSON>'s AI Agent", "response_nl": "Hallo! <PERSON><PERSON> ben <PERSON>'s Agent <PERSON> bot. Hoe kan ik je helpen met je AI-project?"}, {"command": "/help", "description": "❓ Hulp en commando's", "response_nl": "Beschikbare commando's:\n/trading - Trading bot info\n/webapp - Web app ontwikkeling\n/crm - CRM integratie\n/figma - Figma naar code conversie"}, {"command": "/trading", "description": "📈 Trading bot ontwikkeling", "response_nl": "Laten we een trading bot bouwen! Welke markt wil je analyseren? (crypto, forex, aandelen)"}, {"command": "/webapp", "description": "💻 Web applicatie ontwikkeling", "response_nl": "Perfect! Laten we een web app maken. Heb je een Figma design of zal ik een moderne UI voorstellen?"}, {"command": "/crm", "description": "📊 CRM systeem integratie", "response_nl": "CRM integratie setup. Welk systeem gebruik je? (HubSpot, Salesforce, custom)"}, {"command": "/figma", "description": "🎨 Figma naar code conversie", "response_nl": "Deel je Figma link en ik converteer het naar werkende code (React, Vue, of vanilla)"}]}, "whatsapp_bot": {"enabled": true, "business_account_id": "**********", "phone_number_id": "**********", "access_token": "WHATSAPP_ACCESS_TOKEN", "webhook_verify_token": "VERIFY_TOKEN", "auto_responses": {"greeting_nl": "👋 Hallo! <PERSON><PERSON> is <PERSON>'s AI Assistant. Ik help je met:\n\n🤖 <PERSON><PERSON> ontwikkeling\n📈 Trading systemen\n💻 Web/mobile apps\n🔧 Workflow automation\n\nWaarmee kan ik je vandaag helpen?", "trading_keywords": ["trading", "bot", "crypto", "forex", "<PERSON><PERSON><PERSON><PERSON>", "analyse"], "dev_keywords": ["website", "app", "code", "react", "vue", "python", "javascript"], "crm_keywords": ["crm", "klanten", "leads", "sales", "hubspot"]}}, "leon_specializations": {"telegram_whatsapp_bots": {"description": "Expert in Telegram en WhatsApp bot ontwikkeling", "tech_stack": ["python-telegram-bot", "whatsapp-business-api", "webhook-integration"], "features": ["Real-time messaging", "Custom keyboards", "File handling", "Payment integration", "Multi-language support"]}, "trading_bots": {"description": "Geavanceerde trading bot ontwikkeling met AI", "tech_stack": ["ccxt", "pandas", "numpy", "ta-lib", "machine-learning"], "features": ["Multiple exchange support", "Technical analysis", "Risk management", "Backtesting", "Real-time alerts"]}, "crm_systems": {"description": "CRM integratie en workflow automation", "tech_stack": ["hubspot-api", "salesforce-api", "zapier", "webhooks"], "features": ["Lead automation", "Contact synchronization", "Custom workflows", "Reporting dashboards", "Integration APIs"]}, "web_mobile_apps": {"description": "Moderne web en mobile applicatie ontwikkeling", "tech_stack": ["react", "vue", "nextjs", "react-native", "pwa"], "features": ["Responsive design", "PWA capabilities", "Real-time updates", "Mobile-first approach", "SEO optimization"]}}, "leon_workflow_automation": {"figma_to_code": {"enabled": true, "supported_frameworks": ["react", "vue", "angular", "vanilla"], "features": ["Automatic component generation", "CSS extraction", "Responsive breakpoints", "Asset optimization"]}, "ai_agents": {"enabled": true, "capabilities": ["Code generation", "Documentation writing", "Testing automation", "Deployment scripts", "Performance optimization"]}}, "leon_preferences": {"code_style": {"minimal_comments": true, "direct_usable_code": true, "clean_architecture": true, "performance_focused": true}, "development_approach": {"local_ai_preferred": true, "json_strategies": true, "api_first_design": true, "mobile_responsive": true}, "communication": {"language": "nederlands", "developer_focused": true, "three_options_always": true, "surprise_option_included": true}}, "alerts": {"trading_signals": {"enabled": true, "channels": ["telegram", "whatsapp", "email"], "frequency": "real-time"}, "system_monitoring": {"enabled": true, "metrics": ["uptime", "response_time", "error_rate"], "notifications": "critical_only"}}, "integrations": {"databases": ["postgresql", "mongodb", "redis"], "cloud_services": ["aws", "gcp", "digitalocean"], "payment_gateways": ["stripe", "mollie", "ideal"], "external_apis": ["google-apis", "openai", "anthropic", "alpha-vantage"]}, "telegram": {"enabled": true, "token": "**********************************************", "webhook_url": "http://localhost:PORT/api/telegram/webhook", "commands": {"/start": "Welkom bij Agent Zero! Hoe kan ik je helpen?", "/help": "Beschikbare commando's: /start, /help, /status, /memory", "/status": "Agent Zero status check", "/memory": "Geheugen overzicht"}, "chat_settings": {"max_message_length": 4096, "response_timeout": 30, "typing_delay": 1.5}}, "whatsapp": {"enabled": false, "api_key": "YOUR_WHATSAPP_API_KEY", "phone_number": "YOUR_WHATSAPP_BUSINESS_NUMBER", "webhook_url": "http://localhost:PORT/api/whatsapp/webhook", "message_templates": {"welcome": "Hallo! <PERSON><PERSON> ben <PERSON>, je AI-assistent. <PERSON><PERSON><PERSON> kan ik je helpen?", "error": "Sorry, er is iets misgegaan. Probeer het opnieuw.", "processing": "Bezig met verwerken... ⏳"}}, "agent_zero_integration": {"api_base_url": "http://localhost:PORT/api", "endpoints": {"message": "/message", "chat_export": "/chat_export", "memory": "/memory", "scheduler": "/scheduler_task_create"}, "auto_responses": {"enabled": true, "max_response_time": 25, "fallback_message": "<PERSON>k ben <PERSON>eel bezig, probeer het over een moment opnieuw."}}, "workflow_automation": {"trading_alerts": {"enabled": true, "price_threshold": 0.05, "symbols": ["BTC", "ETH"], "notification_channels": ["telegram", "whatsapp"]}, "crm_integration": {"enabled": true, "sync_contacts": true, "auto_follow_up": true, "lead_scoring": true}, "scheduled_messages": {"enabled": true, "daily_updates": "09:00", "weekly_reports": "monday_10:00"}}, "security": {"allowed_users": [], "rate_limiting": {"messages_per_minute": 20, "burst_limit": 5}, "encryption": {"enabled": true, "algorithm": "AES-256"}}, "logging": {"level": "INFO", "file_path": "./logs/bot_activity.log", "max_file_size": "10MB", "backup_count": 5}}