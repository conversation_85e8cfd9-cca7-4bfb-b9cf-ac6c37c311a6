# Leon's Agent Zero - Railway Environment Variables
# Kopieer deze naar Railway Variables tab

# OpenRouter API Key (VERPLICHT!)
API_KEY_OPENROUTER=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

# OpenAI API Key (OPTIONEEL - als backup)
API_KEY_OPENAI=********************************************************************************************************************************************************************

# DeepSeek via OpenRouter (gebruik deze settings)
DEEPSEEK_CHAT_MODEL=deepseek/deepseek-chat
DEEPSEEK_CODER_MODEL=deepseek/deepseek-coder
DEEPSEEK_R1_MODEL=deepseek/deepseek-r1

# Port configuration (Railway zorgt hiervoor)
# PORT wordt automatisch door Railway gezet

# Optional - Andere providers
API_KEY_ANTHROPIC=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

# Trading Bot (optioneel)
MEXC_API_KEY=mx0vglmGyrf9LW7tY9
MEXC_API_SECRET=b5bb5493128b483c9ebe199d013042c1
TRADING_ENABLED=true

# Telegram Bot (optioneel)
TELEGRAM_BOT_TOKEN=**********************************************

# System settings
DEBUG=true
LOG_LEVEL=INFO
PYTHON_UNBUFFERED=1

# Base URLs
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Extra settings
USE_CLOUDFLARE=true 