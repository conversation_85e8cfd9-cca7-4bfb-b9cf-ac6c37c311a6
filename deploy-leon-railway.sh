#!/bin/bash
# <PERSON>'s Specifieke Railway Deploy Script
# Project ID: ************************************

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}"
echo "🚀 <PERSON>'s Agent Zero - Railway Deploy"
echo "====================================="
echo "📱 Project: ************************************"
echo "🌍 URL: agent-zero-production-923b1470.up.railway.app"
echo "====================================="
echo -e "${NC}"

# Check Railway CLI
if ! command -v railway &> /dev/null; then
    echo -e "${BLUE}📦 Installing Railway CLI...${NC}"
    npm install -g @railway/cli
fi

# Login check
echo -e "${BLUE}🔐 Checking Railway authentication...${NC}"
if ! railway whoami &> /dev/null; then
    echo -e "${YELLOW}🔑 Please login to Railway:${NC}"
    railway login
fi

# Link to specific project
echo -e "${BLUE}🔗 Linking to Leon's project...${NC}"
railway link ************************************

# Set environment variables
echo -e "${BLUE}⚙️ Setting environment variables...${NC}"
railway variables set API_KEY_OPENROUTER=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
railway variables set API_KEY_DEEPSEEK=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
railway variables set MEXC_API_KEY=mx0vglmGyrf9LW7tY9
railway variables set MEXC_API_SECRET=b5bb5493128b483c9ebe199d013042c1
railway variables set TELEGRAM_BOT_TOKEN=**********************************************
railway variables set TELEGRAM_TRADING_PASSWORD=Fatima23
railway variables set PORT=50001
railway variables set PYTHONUNBUFFERED=1
railway variables set RAILWAY_ENVIRONMENT=production

# Commit changes
echo -e "${BLUE}💾 Committing changes...${NC}"
git add .
git commit -m "Leon's Agent Zero - Railway deployment ready" || echo "No changes to commit"

# Deploy
echo -e "${BLUE}🚀 Deploying to Railway...${NC}"
railway up

echo -e "${GREEN}"
echo "🎉 Leon's Agent Zero - Deployment Complete!"
echo "==========================================="
echo "🌍 Live URL: https://agent-zero-production-923b1470.up.railway.app"
echo "🔧 Health: https://agent-zero-production-923b1470.up.railway.app/health"
echo "📊 Dashboard: railway open"
echo "📋 Logs: railway logs"
echo "==========================================="
echo -e "${NC}"

# Show status
echo -e "${BLUE}📊 Deployment status:${NC}"
railway status 