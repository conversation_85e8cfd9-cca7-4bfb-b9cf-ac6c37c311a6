# 🚀 MEXC Trading Bot Setup Guide

## 📋 Overzicht

MEXC Exchange integratie voor Agent Zero met:
- Live trading via Telegram commands
- DCA (Dollar Cost Averaging) strategie
- Real-time prijzen en portfolio tracking
- Veilige API integratie met wachtwoord beveiliging

## 🔧 1. MEXC API Setup

### Stap 1: Account Aanmaken
1. Ga naar [MEXC.com](https://www.mexc.com)
2. Registreer account met email
3. Voltooi KYC verificatie (optioneel maar aanbevolen)

### Stap 2: API Keys Genereren
1. Login op MEXC
2. Ga naar: Account → API Management
3. Klik "Create API"
4. Geef API een naam (bijv. "AgentZero Trading")
5. Zet permissies:
   - ✅ Read (Lezen)
   - ✅ Spot Trading
   - ❌ Withdraw (uit veiligheid)
6. IP Whitelist (optioneel maar veiliger)
7. Sla API Key en Secret veilig op!

## 🛠️ 2. Bot Configuratie

### Stap 1: Environment Variables
Kopieer en update `.env` file:

```bash
# MEXC API Keys
MEXC_API_KEY=your_api_key_here
MEXC_API_SECRET=your_api_secret_here

# Trading Settings
TRADING_ENABLED=false  # Zet op true voor live trading
MAX_TRADE_AMOUNT_EUR=100
TELEGRAM_TRADING_PASSWORD=kies_een_veilig_wachtwoord

# Allowed Traders (Telegram Chat IDs)
ALLOWED_TRADING_CHAT_IDS=**********,andere_chat_id
```

### Stap 2: Test de Configuratie
```bash
# Activeer environment
source agent-zero-env/bin/activate

# Test MEXC connectie
python -c "from python.tools.mexc_trading import MEXCTrading; m = MEXCTrading(); print('MEXC Configured:', m.configured)"
```

## 📱 3. Telegram Bot Commands

### Trading Commands (met wachtwoord)
```
/buy BTC 50 jouwwachtwoord    # Koop €50 Bitcoin
/sell ETH 0.5 jouwwachtwoord  # Verkoop 0.5 ETH
/dca SOL 100 jouwwachtwoord   # DCA €100 in Solana
```

### Info Commands (geen wachtwoord)
```
/balance         # Check MEXC balances
/price BTC       # Check Bitcoin prijs
/orders          # Bekijk open orders
/trades BTCUSDT  # Recente trades
/help            # Alle commands
```

## 🚦 4. Bot Starten

### Optie 1: Telegram Bot Only
```bash
python telegram_mexc_bot.py
```

### Optie 2: Met Agent Zero UI
```bash
python run_telegram_bot.py
```

### Optie 3: Docker Setup
```bash
docker-compose -f docker-compose-claude.yml up -d
```

## 🔒 5. Veiligheid

### Belangrijke Tips:
1. **Gebruik ALTIJD een sterk trading wachtwoord**
2. **Begin met kleine bedragen (€10-20)**
3. **Test eerst met TRADING_ENABLED=false**
4. **Whitelist alleen je eigen Telegram Chat ID**
5. **Gebruik geen withdrawal permissions**

### API Limits:
- Max 20 requests per seconde
- Max 100 orders per 10 seconden
- DCA minimaal €10 per trade

## 📊 6. Trading Strategieën

### DCA (Dollar Cost Averaging)
Perfect voor beginners:
```
/dca BTC 50 wachtwoord  # Koop elke keer €50 BTC
```

### Grid Trading (Coming Soon)
Automatisch kopen laag, verkopen hoog

### Scalping (Coming Soon)
Snelle trades op kleine prijsbewegingen

## 🐛 7. Troubleshooting

### "API niet geconfigureerd"
- Check MEXC_API_KEY en MEXC_API_SECRET in .env

### "Verkeerd trading wachtwoord"
- Check TELEGRAM_TRADING_PASSWORD in .env

### "Geen trading permissie"
- Voeg je Chat ID toe aan ALLOWED_TRADING_CHAT_IDS

### "MEXC API error"
- Check API permissions op MEXC
- Controleer IP whitelist settings

## 📈 8. Advanced Features

### Price Alerts
```python
# In development: Automatische alerts bij prijsveranderingen
```

### Portfolio Tracking
```python
# Real-time portfolio waarde tracking
```

### Multi-Exchange Support
```python
# Binance, Bitvavo integratie mogelijk
```

## 💡 9. Tips & Tricks

1. **Start Klein**: Begin met €10-20 trades
2. **DCA is King**: Regelmatig kleine bedragen werkt beter
3. **Geen FOMO**: Stick to your strategy
4. **Track Everything**: Houd een trade journal bij
5. **Learn First**: Begrijp wat je doet

## 📞 10. Support

- MEXC Support: <EMAIL>
- API Docs: https://mexcglobal.github.io/apidocs/
- Telegram: @InnovarsLabo

---

**⚠️ DISCLAIMER**: Trading involves risk. Only trade what you can afford to lose. This bot is for educational purposes. Always DYOR (Do Your Own Research)! 