# Agent Zero - Leon's Nederlandse AI Development Environment
# Claude 3.5 Sonnet Configuration

API_KEY_ANTHROPIC=************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************

# Optioneel: openrouter voor embeddings en whisper (vervang indien beschikbaar)
API_KEY_OPENAI=sk-...

# RFC disabled - geen Docker problemen
RFC_AUTO_DOCKER=false
RFC_ENABLED=false
DISABLE_RFC=true

# Leon's Web UI settings
WEB_UI_HOST=0.0.0.0
WEB_UI_PORT=50001

# Leon's Developer Settings
DEVELOPER_NAME=Leon
DEVELOPER_LANG=nl
BOT_DEVELOPMENT=true
TRADING_BOTS=true
FIGMA_TO_CODE=true

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_ENABLED=true

# WhatsApp Bot Configuration  
WHATSAPP_BUSINESS_ID=6229184945
WHATSAPP_PHONE_ID=0470835685
WHATSAPP_ENABLED=true

# Claude Model Configuration
CLAUDE_MODEL=claude-3-5-sonnet-20241022
CLAUDE_MAX_TOKENS=8192
CLAUDE_TEMPERATURE=0.7
CLAUDE_CONTEXT_LENGTH=200000

# Leon's Workflow Settings
MINIMAL_COMMENTS=true
DIRECT_USABLE_CODE=true
LOCAL_AI_PRIORITY=true
JSON_STRATEGIES=true
THREE_OPTIONS_ALWAYS=true

# Development Environment
NODE_ENV=development
PYTHONPATH=. 