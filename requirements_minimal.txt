# Voeg toe aan je requirements.txt
# === Core AI Stack ===
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
torch==2.0.0
torchaudio==2.0.0
nemo-toolkit[all]==1.22.0
requests==2.31.0
numpy==1.24.3

# === <PERSON>'s Complete AI Stack ===
# LangChain voor workflows
langchain==0.1.0
langchain-community==0.0.13
langchain-core==0.1.0

# Whisper voor speech-to-text
openai-whisper==20231117
soundfile==0.12.1

# ChromaDB voor vector storage  
chromadb==0.4.22
sentence-transformers==2.2.2

# Ollama client
ollama==0.1.7

# AutoGen voor multi-agent
pyautogen==0.2.0

# Dashboard
streamlit==1.29.0
plotly==5.17.0

# Agent Zero integration
websockets==12.0
aiofiles==23.2.1
python-multipart==0.0.6

# <PERSON> voice optimizations
librosa==0.10.1
scipy==1.11.4
